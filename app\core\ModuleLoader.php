<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Container;
use App\Core\Router;
use Exception;

/**
 * Module Loader
 * Handles loading and management of modular components
 * 
 * @package App\Core
 */
class ModuleLoader
{
    private Container $container;
    private array $modules = [];
    private array $loadedModules = [];
    private string $modulesPath;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->modulesPath = APP_PATH . '/modules';
        $this->discoverModules();
    }

    /**
     * Discover available modules
     */
    private function discoverModules(): void
    {
        if (!is_dir($this->modulesPath)) {
            mkdir($this->modulesPath, 0755, true);
            return;
        }

        $directories = array_filter(glob($this->modulesPath . '/*'), 'is_dir');
        
        foreach ($directories as $dir) {
            $moduleName = basename($dir);
            $configFile = $dir . '/module.json';
            
            if (file_exists($configFile)) {
                $config = json_decode(file_get_contents($configFile), true);
                
                if ($config && $this->validateModuleConfig($config)) {
                    $this->modules[$moduleName] = array_merge($config, [
                        'path' => $dir,
                        'name' => $moduleName
                    ]);
                }
            } else {
                // Create default module config
                $this->modules[$moduleName] = [
                    'name' => $moduleName,
                    'version' => '1.0.0',
                    'description' => ucfirst($moduleName) . ' module',
                    'enabled' => true,
                    'dependencies' => [],
                    'path' => $dir
                ];
            }
        }
    }

    /**
     * Validate module configuration
     */
    private function validateModuleConfig(array $config): bool
    {
        $required = ['name', 'version', 'enabled'];
        
        foreach ($required as $field) {
            if (!isset($config[$field])) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Load all enabled modules
     */
    public function loadModules(): void
    {
        // Sort modules by dependencies
        $sortedModules = $this->sortModulesByDependencies();
        
        foreach ($sortedModules as $moduleName) {
            $this->loadModule($moduleName);
        }
    }

    /**
     * Load a specific module
     */
    public function loadModule(string $moduleName): bool
    {
        if (isset($this->loadedModules[$moduleName])) {
            return true;
        }

        if (!isset($this->modules[$moduleName])) {
            throw new Exception("Module [{$moduleName}] not found");
        }

        $module = $this->modules[$moduleName];
        
        if (!$module['enabled']) {
            return false;
        }

        // Load dependencies first
        if (!empty($module['dependencies'])) {
            foreach ($module['dependencies'] as $dependency) {
                if (!$this->loadModule($dependency)) {
                    throw new Exception("Failed to load dependency [{$dependency}] for module [{$moduleName}]");
                }
            }
        }

        // Load module bootstrap file
        $bootstrapFile = $module['path'] . '/bootstrap.php';
        if (file_exists($bootstrapFile)) {
            require_once $bootstrapFile;
        }

        // Register module services
        $this->registerModuleServices($module);

        // Load module configuration
        $this->loadModuleConfig($module);

        $this->loadedModules[$moduleName] = $module;
        
        return true;
    }

    /**
     * Register module services in container
     */
    private function registerModuleServices(array $module): void
    {
        $servicesFile = $module['path'] . '/services.php';
        
        if (file_exists($servicesFile)) {
            $services = require $servicesFile;
            
            if (is_array($services)) {
                foreach ($services as $abstract => $concrete) {
                    $this->container->bind($abstract, $concrete);
                }
            }
        }
    }

    /**
     * Load module configuration
     */
    private function loadModuleConfig(array $module): void
    {
        $configDir = $module['path'] . '/config';
        
        if (is_dir($configDir)) {
            $configFiles = glob($configDir . '/*.php');
            
            foreach ($configFiles as $file) {
                $configName = basename($file, '.php');
                $config = require $file;
                
                // Store module config globally
                $GLOBALS['module_config'][$module['name']][$configName] = $config;
            }
        }
    }

    /**
     * Load module routes
     */
    public function loadModuleRoutes(Router $router): void
    {
        foreach ($this->loadedModules as $module) {
            $routesFile = $module['path'] . '/routes.php';
            
            if (file_exists($routesFile)) {
                // Create module route group
                $router->group([
                    'prefix' => $module['name'],
                    'namespace' => 'App\\Modules\\' . ucfirst($module['name']) . '\\Controllers'
                ], function ($router) use ($routesFile) {
                    require $routesFile;
                });
            }
        }
    }

    /**
     * Sort modules by dependencies
     */
    private function sortModulesByDependencies(): array
    {
        $sorted = [];
        $visited = [];
        $visiting = [];

        foreach (array_keys($this->modules) as $moduleName) {
            if (!isset($visited[$moduleName])) {
                $this->visitModule($moduleName, $visited, $visiting, $sorted);
            }
        }

        return array_reverse($sorted);
    }

    /**
     * Visit module for dependency sorting
     */
    private function visitModule(string $moduleName, array &$visited, array &$visiting, array &$sorted): void
    {
        if (isset($visiting[$moduleName])) {
            throw new Exception("Circular dependency detected for module [{$moduleName}]");
        }

        if (isset($visited[$moduleName])) {
            return;
        }

        $visiting[$moduleName] = true;

        $module = $this->modules[$moduleName];
        $dependencies = $module['dependencies'] ?? [];

        foreach ($dependencies as $dependency) {
            if (!isset($this->modules[$dependency])) {
                throw new Exception("Dependency [{$dependency}] not found for module [{$moduleName}]");
            }
            
            $this->visitModule($dependency, $visited, $visiting, $sorted);
        }

        unset($visiting[$moduleName]);
        $visited[$moduleName] = true;
        $sorted[] = $moduleName;
    }

    /**
     * Get loaded modules
     */
    public function getLoadedModules(): array
    {
        return $this->loadedModules;
    }

    /**
     * Get all modules
     */
    public function getAllModules(): array
    {
        return $this->modules;
    }

    /**
     * Check if module is loaded
     */
    public function isModuleLoaded(string $moduleName): bool
    {
        return isset($this->loadedModules[$moduleName]);
    }

    /**
     * Enable module
     */
    public function enableModule(string $moduleName): bool
    {
        if (!isset($this->modules[$moduleName])) {
            return false;
        }

        $this->modules[$moduleName]['enabled'] = true;
        $this->saveModuleConfig($moduleName);
        
        return true;
    }

    /**
     * Disable module
     */
    public function disableModule(string $moduleName): bool
    {
        if (!isset($this->modules[$moduleName])) {
            return false;
        }

        $this->modules[$moduleName]['enabled'] = false;
        $this->saveModuleConfig($moduleName);
        
        // Remove from loaded modules
        unset($this->loadedModules[$moduleName]);
        
        return true;
    }

    /**
     * Save module configuration
     */
    private function saveModuleConfig(string $moduleName): void
    {
        $module = $this->modules[$moduleName];
        $configFile = $module['path'] . '/module.json';
        
        $config = $module;
        unset($config['path']); // Don't save path in config
        
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
    }

    /**
     * Get module configuration
     */
    public function getModuleConfig(string $moduleName, string $configName = null)
    {
        if (!isset($GLOBALS['module_config'][$moduleName])) {
            return null;
        }

        if ($configName === null) {
            return $GLOBALS['module_config'][$moduleName];
        }

        return $GLOBALS['module_config'][$moduleName][$configName] ?? null;
    }

    /**
     * Create new module
     */
    public function createModule(string $moduleName, array $config = []): bool
    {
        $modulePath = $this->modulesPath . '/' . $moduleName;

        if (is_dir($modulePath)) {
            return false; // Module already exists
        }

        // Create module directory structure
        $directories = [
            $modulePath,
            $modulePath . '/controllers',
            $modulePath . '/models',
            $modulePath . '/views',
            $modulePath . '/config',
            $modulePath . '/services'
        ];

        foreach ($directories as $dir) {
            mkdir($dir, 0755, true);
        }

        // Create module configuration
        $defaultConfig = [
            'name' => $moduleName,
            'version' => '1.0.0',
            'description' => ucfirst($moduleName) . ' module',
            'enabled' => true,
            'dependencies' => []
        ];

        $moduleConfig = array_merge($defaultConfig, $config);
        file_put_contents($modulePath . '/module.json', json_encode($moduleConfig, JSON_PRETTY_PRINT));

        // Create basic files
        $this->createModuleFiles($modulePath, $moduleName);

        // Add to modules array
        $this->modules[$moduleName] = array_merge($moduleConfig, ['path' => $modulePath]);

        return true;
    }

    /**
     * Create basic module files
     */
    private function createModuleFiles(string $modulePath, string $moduleName): void
    {
        // Create routes file
        file_put_contents($modulePath . '/routes.php', "<?php\n\n// {$moduleName} module routes\n");

        // Create services file
        file_put_contents($modulePath . '/services.php', "<?php\n\nreturn [\n    // Module services\n];\n");

        // Create bootstrap file
        file_put_contents($modulePath . '/bootstrap.php', "<?php\n\n// {$moduleName} module bootstrap\n");
    }
}
