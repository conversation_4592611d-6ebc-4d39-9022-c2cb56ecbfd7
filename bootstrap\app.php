<?php

/**
 * JobSpace Application Bootstrap
 * High-Performance Modular Platform Bootstrap
 *
 * @package JobSpace
 * @version 1.0.0
 * <AUTHOR> Team
 */

declare(strict_types=1);

// Define application constants
define('APP_START_TIME', microtime(true));
define('APP_START_MEMORY', memory_get_usage());

// Define directory paths
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('CACHE_PATH', STORAGE_PATH . '/cache');
define('LOGS_PATH', STORAGE_PATH . '/logs');
define('UPLOADS_PATH', STORAGE_PATH . '/uploads');

// Load environment variables
if (file_exists(ROOT_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(ROOT_PATH);
    $dotenv->load();
}

// Load Composer autoloader
require_once ROOT_PATH . '/vendor/autoload.php';

// Load core helpers
require_once APP_PATH . '/core/helpers.php';

// Initialize error handling
error_reporting(E_ALL);
ini_set('display_errors', $_ENV['APP_DEBUG'] ?? '0');
ini_set('log_errors', '1');
ini_set('error_log', LOGS_PATH . '/php_errors.log');

// Set timezone
date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'UTC');

// Set memory limit for high-performance operations
ini_set('memory_limit', $_ENV['APP_MEMORY_LIMIT'] ?? '512M');

// Initialize the application
try {
    $app = new App\Core\Application();
    return $app;
} catch (Throwable $e) {
    // Emergency error handling
    error_log("Bootstrap Error: " . $e->getMessage());

    if (($_ENV['APP_DEBUG'] ?? false)) {
        throw $e;
    }

    // Show user-friendly error page
    http_response_code(500);
    echo "Application temporarily unavailable. Please try again later.";
    exit(1);
}