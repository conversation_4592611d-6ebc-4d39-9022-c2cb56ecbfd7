<?php

use App\Core\Database;

return new class {
    /**
     * Run the migration
     */
    public function up(Database $db): void
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS testimonials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NULL,
                name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
                email VARCHAR(255) NULL,
                company VARCHAR(255) NULL,
                position VARCHAR(255) NULL,
                content TEXT NOT NULL,
                rating DECIMAL(2,1) DEFAULT 5.0,
                avatar VARCHAR(255) NULL,
                status ENUM('pending', 'active', 'rejected') DEFAULT 'pending',
                featured BOOLEAN DEFAULT FALSE,
                module VARCHAR(50) DEFAULT 'general',
                approved_by INT NULL,
                approved_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                <PERSON>OREIG<PERSON> KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_status (status),
                INDEX idx_featured (featured),
                INDEX idx_module (module),
                INDEX idx_rating (rating),
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
        ";
        
        $db->execute($sql);
    }

    /**
     * Reverse the migration
     */
    public function down(Database $db): void
    {
        $db->execute("DROP TABLE IF EXISTS testimonials");
    }
};
