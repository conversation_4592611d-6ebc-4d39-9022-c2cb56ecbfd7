<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Container;
use App\Core\Router;
use App\Core\Request;
use App\Core\Response;
use App\Core\Database;
use App\Core\Session;
use App\Core\Auth;
use App\Core\ModuleLoader;
use App\Core\ExceptionHandler;
use App\Shared\Middleware\CsrfMiddleware;
use App\Shared\Middleware\RateLimitMiddleware;
use Exception;
use Throwable;

/**
 * Main Application Class
 * Handles application lifecycle and coordinates all core components
 * 
 * @package App\Core
 */
class Application
{
    private Container $container;
    private Router $router;
    private Request $request;
    private Response $response;
    private ModuleLoader $moduleLoader;
    private array $middleware = [];
    private bool $booted = false;

    public function __construct()
    {
        $this->container = new Container();
        $this->registerCoreServices();
        $this->boot();
    }

    /**
     * Register core services in the container
     */
    private function registerCoreServices(): void
    {
        // Register core services
        $this->container->singleton(Database::class, function () {
            return new Database();
        });

        $this->container->singleton(Session::class, function () {
            return new Session();
        });

        $this->container->singleton(Auth::class, function () {
            return new Auth($this->container->get(Database::class));
        });

        $this->container->singleton(Router::class, function () {
            return new Router($this->container);
        });

        $this->container->singleton(Request::class, function () {
            return Request::createFromGlobals();
        });

        $this->container->singleton(Response::class, function () {
            return new Response();
        });

        $this->container->singleton(ModuleLoader::class, function () {
            return new ModuleLoader($this->container);
        });

        // Register exception handler
        $this->container->singleton(ExceptionHandler::class, function () {
            return new ExceptionHandler();
        });
    }

    /**
     * Boot the application
     */
    private function boot(): void
    {
        if ($this->booted) {
            return;
        }

        // Get core services
        $this->router = $this->container->get(Router::class);
        $this->request = $this->container->get(Request::class);
        $this->response = $this->container->get(Response::class);
        $this->moduleLoader = $this->container->get(ModuleLoader::class);

        // Initialize session
        $session = $this->container->get(Session::class);
        $session->start();

        // Set up exception handling
        $exceptionHandler = $this->container->get(ExceptionHandler::class);
        $exceptionHandler->register();

        // Register global middleware
        $this->registerGlobalMiddleware();

        // Load modules
        $this->moduleLoader->loadModules();

        // Load routes
        $this->loadRoutes();

        $this->booted = true;
    }

    /**
     * Register global middleware
     */
    private function registerGlobalMiddleware(): void
    {
        $this->middleware = [
            CsrfMiddleware::class,
            RateLimitMiddleware::class,
        ];
    }

    /**
     * Load application routes
     */
    private function loadRoutes(): void
    {
        // Load web routes
        if (file_exists(ROOT_PATH . '/routes/web.php')) {
            require ROOT_PATH . '/routes/web.php';
        }

        // Load API routes
        if (file_exists(ROOT_PATH . '/routes/api.php')) {
            require ROOT_PATH . '/routes/api.php';
        }

        // Load module routes
        $this->moduleLoader->loadModuleRoutes($this->router);
    }

    /**
     * Handle incoming request
     */
    public function handle(Request $request = null): Response
    {
        try {
            $request = $request ?: $this->request;
            
            // Apply global middleware
            foreach ($this->middleware as $middlewareClass) {
                $middleware = new $middlewareClass();
                $request = $middleware->handle($request);
            }

            // Route the request
            $response = $this->router->dispatch($request);
            
            return $response;
            
        } catch (Throwable $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Handle exceptions
     */
    private function handleException(Throwable $e): Response
    {
        $exceptionHandler = $this->container->get(ExceptionHandler::class);
        return $exceptionHandler->handle($e, $this->request);
    }

    /**
     * Run the application
     */
    public function run(): void
    {
        $response = $this->handle();
        $response->send();
    }

    /**
     * Get container instance
     */
    public function getContainer(): Container
    {
        return $this->container;
    }

    /**
     * Get service from container
     */
    public function get(string $abstract)
    {
        return $this->container->get($abstract);
    }

    /**
     * Check if application is booted
     */
    public function isBooted(): bool
    {
        return $this->booted;
    }

    /**
     * Get application version
     */
    public function version(): string
    {
        return '1.0.0';
    }

    /**
     * Get application environment
     */
    public function environment(): string
    {
        return $_ENV['APP_ENV'] ?? 'production';
    }

    /**
     * Check if application is in debug mode
     */
    public function isDebug(): bool
    {
        return (bool) ($_ENV['APP_DEBUG'] ?? false);
    }
}
