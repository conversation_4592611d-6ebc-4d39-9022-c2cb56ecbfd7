-- JobSpace Database Setup
-- Quick setup for testing the application

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS jobspace CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE jobspace;

-- Basic users table for testing
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'creator', 'business', 'user') DEFAULT 'user',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    email_verified_at TIMESTAMP NULL,
    email_verification_token VARCHAR(255) NULL,
    last_login_at TIMESTAMP NULL,
    last_login_ip VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_role (role)
) ENGINE=InnoDB;

-- User roles table
CREATE TABLE IF NOT EXISTS user_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    role_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_role (user_id, role_name)
) ENGINE=InnoDB;

-- Basic testimonials table
CREATE TABLE IF NOT EXISTS testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NULL,
    company VARCHAR(255) NULL,
    position VARCHAR(255) NULL,
    content TEXT NOT NULL,
    rating DECIMAL(2,1) DEFAULT 5.0,
    avatar VARCHAR(255) NULL,
    status ENUM('pending', 'active', 'rejected') DEFAULT 'pending',
    featured BOOLEAN DEFAULT FALSE,
    module VARCHAR(50) DEFAULT 'general',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_module (module)
) ENGINE=InnoDB;

-- Basic statistics table
CREATE TABLE IF NOT EXISTS statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value BIGINT NOT NULL,
    metric_type ENUM('counter', 'gauge', 'histogram') DEFAULT 'counter',
    module VARCHAR(50) DEFAULT 'general',
    date_recorded TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric_name (metric_name),
    INDEX idx_module (module),
    INDEX idx_date (date_recorded)
) ENGINE=InnoDB;

-- User activity logs
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB;

-- Failed login attempts
CREATE TABLE IF NOT EXISTS failed_login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_ip (ip_address),
    INDEX idx_attempted_at (attempted_at)
) ENGINE=InnoDB;

-- User remember tokens
CREATE TABLE IF NOT EXISTS user_remember_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB;

-- Insert sample data for testing
INSERT INTO users (name, email, password, role, status, email_verified_at) VALUES
('Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', NOW()),
('John Creator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'creator', 'active', NOW()),
('Business User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'business', 'active', NOW()),
('Regular User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'active', NOW())
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- Insert user roles
INSERT INTO user_roles (user_id, role_name) VALUES
(1, 'admin'),
(2, 'creator'),
(3, 'business'),
(4, 'user')
ON DUPLICATE KEY UPDATE role_name=VALUES(role_name);

-- Insert sample testimonials
INSERT INTO testimonials (name, company, position, content, rating, status, featured, module) VALUES
('Sarah Johnson', 'TechCorp Inc.', 'CEO', 'JobSpace has revolutionized how we manage our projects and find talent. The platform is incredibly user-friendly and efficient.', 5.0, 'active', TRUE, 'general'),
('Mike Chen', 'Design Studio', 'Creative Director', 'The quiz system helped me assess candidates quickly and accurately. Highly recommended for any business.', 4.8, 'active', TRUE, 'quiz'),
('Emily Davis', 'Marketing Pro', 'Freelancer', 'I found amazing job opportunities through JobSpace. The platform connects freelancers with quality clients.', 4.9, 'active', TRUE, 'freelancing'),
('David Wilson', 'E-Shop Owner', 'Entrepreneur', 'The e-commerce features are fantastic. Easy to set up and manage my online store.', 4.7, 'active', FALSE, 'ecommerce'),
('Lisa Brown', 'Social Media Expert', 'Influencer', 'Love the social features! Great way to connect with like-minded professionals.', 4.6, 'active', FALSE, 'social')
ON DUPLICATE KEY UPDATE content=VALUES(content);

-- Insert sample statistics
INSERT INTO statistics (metric_name, metric_value, module) VALUES
('total_users', 1250, 'general'),
('total_quizzes', 45, 'quiz'),
('total_jobs', 128, 'freelancing'),
('total_products', 89, 'ecommerce'),
('total_posts', 567, 'social'),
('quiz_attempts', 3420, 'quiz'),
('successful_hires', 67, 'freelancing'),
('orders_completed', 234, 'ecommerce'),
('social_interactions', 8945, 'social')
ON DUPLICATE KEY UPDATE metric_value=VALUES(metric_value);

-- Create indexes for better performance
CREATE INDEX idx_users_email_status ON users(email, status);
CREATE INDEX idx_testimonials_status_featured ON testimonials(status, featured);
CREATE INDEX idx_statistics_module_metric ON statistics(module, metric_name);

-- Show tables created
SHOW TABLES;
