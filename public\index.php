<?php

/**
 * JobSpace Application Entry Point
 * High-Performance Multi-Platform Web Application
 *
 * @package JobSpace
 * @version 1.0.0
 * <AUTHOR> Team
 */

declare(strict_types=1);

// Define the start time for performance monitoring
define('LARAVEL_START', microtime(true));

try {
    // Bootstrap the application
    $app = require_once __DIR__ . '/../bootstrap/app.php';

    // Handle the incoming request and send response
    $app->run();
} catch (Throwable $e) {
    // Emergency fallback
    http_response_code(500);

    if (($_ENV['APP_DEBUG'] ?? false)) {
        echo "<h1>Application Error</h1>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        echo "<h1>500 - Internal Server Error</h1>";
        echo "<p>Something went wrong. Please try again later.</p>";
    }
}