<?php

use App\Modules\Public\Controllers\HomeController;
use App\Modules\Public\Controllers\PageController;

// Homepage
$router->get('/', [HomeController::class, 'index'], 'home');

// Static pages
$router->get('/about', [PageController::class, 'about'], 'about');
$router->get('/contact', [PageController::class, 'contact'], 'contact');
$router->post('/contact', [PageController::class, 'submitContact'], 'contact.submit');
$router->get('/privacy', [PageController::class, 'privacy'], 'privacy');
$router->get('/terms', [PageController::class, 'terms'], 'terms');
$router->get('/faq', [PageController::class, 'faq'], 'faq');

// API endpoints for public data
$router->get('/api/stats', [HomeController::class, 'getStats'], 'api.stats');
$router->get('/api/testimonials', [HomeController::class, 'getTestimonials'], 'api.testimonials');
