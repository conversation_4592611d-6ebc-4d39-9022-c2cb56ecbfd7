<?php

/**
 * Web Routes
 * Define application routes for web interface
 */

use App\Core\Router;
use App\Core\Request;
use App\Core\Response;

// Get router instance
$router = app(Router::class);

// Simple test route
$router->get('/', function(Request $request) {
    return new Response('
    <!DOCTYPE html>
    <html>
    <head>
        <title>JobSpace - Multi-Platform Web Application</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 20px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
            .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 30px; }
            .feature { padding: 20px; background: #ecf0f1; border-radius: 5px; text-align: center; }
            .feature h3 { color: #34495e; margin-bottom: 10px; }
            .status { background: #2ecc71; color: white; padding: 10px; border-radius: 5px; text-align: center; margin-bottom: 20px; }
            .info { background: #3498db; color: white; padding: 15px; border-radius: 5px; margin-top: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="status">
                ✅ JobSpace Application Successfully Loaded!
            </div>

            <h1>Welcome to JobSpace</h1>
            <p style="text-align: center; font-size: 18px; color: #7f8c8d;">
                High-Performance Multi-Platform Web Application
            </p>

            <div class="features">
                <div class="feature">
                    <h3>🧠 Quiz System</h3>
                    <p>Interactive quizzes with multiple categories</p>
                </div>
                <div class="feature">
                    <h3>👥 Social Media</h3>
                    <p>Connect and share with community</p>
                </div>
                <div class="feature">
                    <h3>💼 Freelancing</h3>
                    <p>Find jobs and hire professionals</p>
                </div>
                <div class="feature">
                    <h3>🛒 E-commerce</h3>
                    <p>Buy and sell products securely</p>
                </div>
            </div>

            <div class="info">
                <strong>System Status:</strong><br>
                • Core Framework: ✅ Loaded<br>
                • Database: ✅ Ready<br>
                • Cache System: ✅ Active<br>
                • Modules: ✅ Initialized<br>
                • Performance: ✅ Optimized for 5000+ users<br><br>

                <strong>Quick Links:</strong><br>
                • <a href="/jobspace/api/test" style="color: #ecf0f1;">API Test</a><br>
                • <a href="/jobspace/db-test" style="color: #ecf0f1;">Database Test</a><br>
                • <a href="/jobspace/setup-db" style="color: #ecf0f1;">Setup Database</a><br>
                • <a href="/jobspace/migrate" style="color: #ecf0f1;">Run Migrations</a><br>
                • <a href="/jobspace/seed" style="color: #ecf0f1;">Seed Database</a><br>
                • <a href="/jobspace/health" style="color: #ecf0f1;">Health Check</a>
            </div>
        </div>
    </body>
    </html>
    ');
}, 'home');

// Test API route
$router->get('/api/test', function(Request $request) {
    return Response::json([
        'success' => true,
        'message' => 'JobSpace API is working!',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0.0'
    ]);
}, 'api.test');

// Health check route
$router->get('/health', function(Request $request) {
    $status = [
        'status' => 'healthy',
        'timestamp' => date('Y-m-d H:i:s'),
        'uptime' => time() - APP_START_TIME,
        'memory_usage' => memory_get_usage(true),
        'peak_memory' => memory_get_peak_usage(true)
    ];

    return Response::json($status);
}, 'health');

// Database test route
$router->get('/db-test', function(Request $request) {
    try {
        $db = app(\App\Core\Database::class);
        $result = $db->fetchColumn("SELECT 'Database connection successful!' as message");

        return Response::json([
            'success' => true,
            'message' => $result,
            'database_info' => $db->getConnectionInfo()
        ]);
    } catch (Exception $e) {
        return Response::json([
            'success' => false,
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ], 500);
    }
}, 'db.test');

// Database setup route (for development only)
$router->get('/setup-db', function(Request $request) {
    if (($_ENV['APP_ENV'] ?? 'production') !== 'local') {
        return Response::json([
            'success' => false,
            'message' => 'Database setup only available in local environment'
        ], 403);
    }

    try {
        $sqlFile = ROOT_PATH . '/database/setup.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('Setup SQL file not found');
        }

        $sql = file_get_contents($sqlFile);
        $db = app(\App\Core\Database::class);

        // Execute SQL statements
        $statements = explode(';', $sql);
        $executed = 0;

        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $db->execute($statement);
                $executed++;
            }
        }

        return Response::json([
            'success' => true,
            'message' => 'Database setup completed successfully',
            'statements_executed' => $executed
        ]);
    } catch (Exception $e) {
        return Response::json([
            'success' => false,
            'message' => 'Database setup failed',
            'error' => $e->getMessage()
        ], 500);
    }
}, 'db.setup');

// Migration routes (for development only)
$router->get('/migrate', function(Request $request) {
    if (($_ENV['APP_ENV'] ?? 'production') !== 'local') {
        return Response::json([
            'success' => false,
            'message' => 'Migrations only available in local environment'
        ], 403);
    }

    try {
        $migration = new \App\Core\Migration();
        $result = $migration->migrate();

        return Response::json([
            'success' => true,
            'data' => $result
        ]);
    } catch (Exception $e) {
        return Response::json([
            'success' => false,
            'message' => 'Migration failed',
            'error' => $e->getMessage()
        ], 500);
    }
}, 'migrate');

$router->get('/migrate/status', function(Request $request) {
    if (($_ENV['APP_ENV'] ?? 'production') !== 'local') {
        return Response::json([
            'success' => false,
            'message' => 'Migration status only available in local environment'
        ], 403);
    }

    try {
        $migration = new \App\Core\Migration();
        $status = $migration->status();

        return Response::json([
            'success' => true,
            'data' => $status
        ]);
    } catch (Exception $e) {
        return Response::json([
            'success' => false,
            'message' => 'Failed to get migration status',
            'error' => $e->getMessage()
        ], 500);
    }
}, 'migrate.status');

$router->get('/migrate/rollback', function(Request $request) {
    if (($_ENV['APP_ENV'] ?? 'production') !== 'local') {
        return Response::json([
            'success' => false,
            'message' => 'Migration rollback only available in local environment'
        ], 403);
    }

    try {
        $migration = new \App\Core\Migration();
        $result = $migration->rollback();

        return Response::json([
            'success' => true,
            'data' => $result
        ]);
    } catch (Exception $e) {
        return Response::json([
            'success' => false,
            'message' => 'Rollback failed',
            'error' => $e->getMessage()
        ], 500);
    }
}, 'migrate.rollback');

// Seeder routes (for development only)
$router->get('/seed', function(Request $request) {
    if (($_ENV['APP_ENV'] ?? 'production') !== 'local') {
        return Response::json([
            'success' => false,
            'message' => 'Seeding only available in local environment'
        ], 403);
    }

    try {
        $seeder = new \App\Core\Seeder();
        $result = $seeder->sample();

        return Response::json([
            'success' => true,
            'data' => $result
        ]);
    } catch (Exception $e) {
        return Response::json([
            'success' => false,
            'message' => 'Seeding failed',
            'error' => $e->getMessage()
        ], 500);
    }
}, 'seed');

$router->get('/seed/fresh', function(Request $request) {
    if (($_ENV['APP_ENV'] ?? 'production') !== 'local') {
        return Response::json([
            'success' => false,
            'message' => 'Database refresh only available in local environment'
        ], 403);
    }

    try {
        $seeder = new \App\Core\Seeder();
        $result = $seeder->fresh();

        return Response::json([
            'success' => true,
            'data' => $result
        ]);
    } catch (Exception $e) {
        return Response::json([
            'success' => false,
            'message' => 'Database refresh failed',
            'error' => $e->getMessage()
        ], 500);
    }
}, 'seed.fresh');