<?php

declare(strict_types=1);

namespace App\Core;

use Exception;

/**
 * View Rendering Engine
 * Handles template rendering with component support and caching
 * 
 * @package App\Core
 */
class View
{
    private array $data = [];
    private array $shared = [];
    private string $viewPath;
    private string $cachePath;
    private bool $cacheEnabled;

    public function __construct()
    {
        $this->viewPath = APP_PATH . '/resources/views';
        $this->cachePath = CACHE_PATH . '/views';
        $this->cacheEnabled = (bool) ($_ENV['VIEW_CACHE'] ?? true);
        
        // Ensure cache directory exists
        if (!is_dir($this->cachePath)) {
            mkdir($this->cachePath, 0755, true);
        }
    }

    /**
     * Render a view
     */
    public function render(string $view, array $data = []): string
    {
        $this->data = array_merge($this->shared, $data);
        
        $viewFile = $this->findViewFile($view);
        
        if (!$viewFile) {
            throw new Exception("View [{$view}] not found");
        }

        // Check for cached version
        if ($this->cacheEnabled) {
            $cacheFile = $this->getCacheFile($view);
            
            if ($this->isCacheValid($viewFile, $cacheFile)) {
                return $this->renderCached($cacheFile);
            }
        }

        // Render and cache
        $content = $this->renderFile($viewFile);
        
        if ($this->cacheEnabled) {
            $this->cacheView($view, $content);
        }
        
        return $content;
    }

    /**
     * Find view file
     */
    private function findViewFile(string $view): ?string
    {
        $view = str_replace('.', '/', $view);
        
        // Check in module views first
        $moduleViewPath = $this->getModuleViewPath($view);
        if ($moduleViewPath && file_exists($moduleViewPath)) {
            return $moduleViewPath;
        }
        
        // Check in main views
        $viewFile = $this->viewPath . '/' . $view . '.php';
        if (file_exists($viewFile)) {
            return $viewFile;
        }
        
        return null;
    }

    /**
     * Get module view path
     */
    private function getModuleViewPath(string $view): ?string
    {
        $parts = explode('/', $view);
        
        if (count($parts) >= 2) {
            $module = $parts[0];
            $viewPath = implode('/', array_slice($parts, 1));
            
            return APP_PATH . "/modules/{$module}/views/{$viewPath}.php";
        }
        
        return null;
    }

    /**
     * Render view file
     */
    private function renderFile(string $file): string
    {
        ob_start();
        
        // Extract data to variables
        extract($this->data, EXTR_SKIP);
        
        // Include the view file
        include $file;
        
        return ob_get_clean();
    }

    /**
     * Render cached view
     */
    private function renderCached(string $cacheFile): string
    {
        ob_start();
        
        // Extract data to variables
        extract($this->data, EXTR_SKIP);
        
        // Include cached file
        include $cacheFile;
        
        return ob_get_clean();
    }

    /**
     * Cache view content
     */
    private function cacheView(string $view, string $content): void
    {
        $cacheFile = $this->getCacheFile($view);
        $cacheDir = dirname($cacheFile);
        
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        file_put_contents($cacheFile, $content);
    }

    /**
     * Get cache file path
     */
    private function getCacheFile(string $view): string
    {
        $view = str_replace('.', '/', $view);
        return $this->cachePath . '/' . $view . '.php';
    }

    /**
     * Check if cache is valid
     */
    private function isCacheValid(string $viewFile, string $cacheFile): bool
    {
        if (!file_exists($cacheFile)) {
            return false;
        }
        
        return filemtime($viewFile) <= filemtime($cacheFile);
    }

    /**
     * Share data with all views
     */
    public function share(string $key, $value): void
    {
        $this->shared[$key] = $value;
    }

    /**
     * Include a component
     */
    public function component(string $component, array $data = []): string
    {
        $componentFile = $this->viewPath . '/components/' . $component . '.php';
        
        if (!file_exists($componentFile)) {
            throw new Exception("Component [{$component}] not found");
        }
        
        // Merge component data with current data
        $componentData = array_merge($this->data, $data);
        
        ob_start();
        extract($componentData, EXTR_SKIP);
        include $componentFile;
        return ob_get_clean();
    }

    /**
     * Include a layout
     */
    public function layout(string $layout, array $data = []): string
    {
        $layoutFile = $this->viewPath . '/layouts/' . $layout . '.php';
        
        if (!file_exists($layoutFile)) {
            throw new Exception("Layout [{$layout}] not found");
        }
        
        // Merge layout data with current data
        $layoutData = array_merge($this->data, $data);
        
        ob_start();
        extract($layoutData, EXTR_SKIP);
        include $layoutFile;
        return ob_get_clean();
    }

    /**
     * Render a partial view
     */
    public function partial(string $partial, array $data = []): string
    {
        $partialFile = $this->viewPath . '/partials/' . $partial . '.php';
        
        if (!file_exists($partialFile)) {
            throw new Exception("Partial [{$partial}] not found");
        }
        
        // Merge partial data with current data
        $partialData = array_merge($this->data, $data);
        
        ob_start();
        extract($partialData, EXTR_SKIP);
        include $partialFile;
        return ob_get_clean();
    }

    /**
     * Clear view cache
     */
    public function clearCache(): void
    {
        $this->deleteDirectory($this->cachePath);
        mkdir($this->cachePath, 0755, true);
    }

    /**
     * Delete directory recursively
     */
    private function deleteDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            
            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }

    /**
     * Check if view exists
     */
    public function exists(string $view): bool
    {
        return $this->findViewFile($view) !== null;
    }

    /**
     * Get view data
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * Get shared data
     */
    public function getShared(): array
    {
        return $this->shared;
    }

    /**
     * Magic method to access view data
     */
    public function __get(string $key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic method to set view data
     */
    public function __set(string $key, $value): void
    {
        $this->data[$key] = $value;
    }

    /**
     * Magic method to check if view data exists
     */
    public function __isset(string $key): bool
    {
        return isset($this->data[$key]);
    }
}
