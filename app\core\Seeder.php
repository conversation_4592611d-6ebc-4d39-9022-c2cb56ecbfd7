<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Database;

/**
 * Database Seeder
 * Handles seeding database with sample data
 * 
 * @package App\Core
 */
class Seeder
{
    private Database $db;
    private string $seedersPath;

    public function __construct()
    {
        $this->db = app(Database::class);
        $this->seedersPath = ROOT_PATH . '/database/seeders';
    }

    /**
     * Run all seeders
     */
    public function run(): array
    {
        $seeders = $this->getAllSeeders();
        $executed = [];

        $this->db->beginTransaction();

        try {
            foreach ($seeders as $seeder) {
                $this->runSeeder($seeder);
                $executed[] = $seeder;
            }

            $this->db->commit();

            return [
                'message' => 'Seeders executed successfully',
                'executed' => $executed
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception("Seeding failed: " . $e->getMessage());
        }
    }

    /**
     * Run specific seeder
     */
    public function runSeeder(string $seederName): void
    {
        $seederFile = $this->seedersPath . '/' . $seederName . '.php';
        
        if (!file_exists($seederFile)) {
            throw new \Exception("Seeder file not found: {$seederName}");
        }

        // Include seeder file
        $seederClass = require $seederFile;
        
        if (!is_object($seederClass) || !method_exists($seederClass, 'run')) {
            throw new \Exception("Invalid seeder file: {$seederName}");
        }

        // Execute seeder
        $seederClass->run($this->db);
    }

    /**
     * Get all seeder files
     */
    private function getAllSeeders(): array
    {
        if (!is_dir($this->seedersPath)) {
            mkdir($this->seedersPath, 0755, true);
            return [];
        }

        $files = glob($this->seedersPath . '/*.php');
        $seeders = [];

        foreach ($files as $file) {
            $seeders[] = basename($file, '.php');
        }

        sort($seeders);
        return $seeders;
    }

    /**
     * Create a new seeder file
     */
    public function create(string $name): string
    {
        $className = $this->studlyCase($name);
        $filename = "{$name}.php";
        $filepath = $this->seedersPath . '/' . $filename;

        if (!is_dir($this->seedersPath)) {
            mkdir($this->seedersPath, 0755, true);
        }

        $template = $this->getSeederTemplate($className);
        file_put_contents($filepath, $template);

        return $filename;
    }

    /**
     * Get seeder template
     */
    private function getSeederTemplate(string $className): string
    {
        return "<?php

use App\Core\Database;

return new class {
    /**
     * Run the seeder
     */
    public function run(Database \$db): void
    {
        // Add your seeding logic here
        \$data = [
            [
                'name' => 'Sample Name',
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        foreach (\$data as \$item) {
            \$db->insert('your_table', \$item);
        }
    }
};
";
    }

    /**
     * Convert string to StudlyCase
     */
    private function studlyCase(string $value): string
    {
        return str_replace(' ', '', ucwords(str_replace(['_', '-'], ' ', $value)));
    }

    /**
     * Truncate all tables (dangerous!)
     */
    public function fresh(): array
    {
        $tables = $this->getAllTables();
        $truncated = [];

        $this->db->beginTransaction();

        try {
            // Disable foreign key checks
            $this->db->execute('SET FOREIGN_KEY_CHECKS = 0');

            foreach ($tables as $table) {
                if ($table !== 'migrations') {
                    $this->db->execute("TRUNCATE TABLE {$table}");
                    $truncated[] = $table;
                }
            }

            // Re-enable foreign key checks
            $this->db->execute('SET FOREIGN_KEY_CHECKS = 1');

            $this->db->commit();

            return [
                'message' => 'Database refreshed successfully',
                'truncated' => $truncated
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception("Database refresh failed: " . $e->getMessage());
        }
    }

    /**
     * Get all tables in database
     */
    private function getAllTables(): array
    {
        $result = $this->db->fetchAll('SHOW TABLES');
        $tables = [];

        foreach ($result as $row) {
            $tables[] = array_values($row)[0];
        }

        return $tables;
    }

    /**
     * Seed with sample data for development
     */
    public function sample(): array
    {
        $this->db->beginTransaction();

        try {
            // Sample users
            $this->seedSampleUsers();
            
            // Sample testimonials
            $this->seedSampleTestimonials();
            
            // Sample statistics
            $this->seedSampleStatistics();

            $this->db->commit();

            return [
                'message' => 'Sample data seeded successfully',
                'seeded' => ['users', 'testimonials', 'statistics']
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception("Sample seeding failed: " . $e->getMessage());
        }
    }

    /**
     * Seed sample users
     */
    private function seedSampleUsers(): void
    {
        $users = [
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => password_hash('password', PASSWORD_DEFAULT),
                'role' => 'admin',
                'status' => 'active',
                'email_verified_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'John Creator',
                'email' => '<EMAIL>',
                'password' => password_hash('password', PASSWORD_DEFAULT),
                'role' => 'creator',
                'status' => 'active',
                'email_verified_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Business User',
                'email' => '<EMAIL>',
                'password' => password_hash('password', PASSWORD_DEFAULT),
                'role' => 'business',
                'status' => 'active',
                'email_verified_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Regular User',
                'email' => '<EMAIL>',
                'password' => password_hash('password', PASSWORD_DEFAULT),
                'role' => 'user',
                'status' => 'active',
                'email_verified_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($users as $user) {
            // Check if user already exists
            $existing = $this->db->fetch('SELECT id FROM users WHERE email = ?', [$user['email']]);
            
            if (!$existing) {
                $userId = $this->db->insert('users', $user);
                
                // Insert user role
                $this->db->insert('user_roles', [
                    'user_id' => $userId,
                    'role_name' => $user['role'],
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    /**
     * Seed sample testimonials
     */
    private function seedSampleTestimonials(): void
    {
        $testimonials = [
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'company' => 'TechCorp Inc.',
                'position' => 'CEO',
                'content' => 'JobSpace has revolutionized how we manage our projects and find talent. The platform is incredibly user-friendly and efficient.',
                'rating' => 5.0,
                'status' => 'active',
                'featured' => true,
                'module' => 'general',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Mike Chen',
                'email' => '<EMAIL>',
                'company' => 'Design Studio',
                'position' => 'Creative Director',
                'content' => 'The quiz system helped me assess candidates quickly and accurately. Highly recommended for any business.',
                'rating' => 4.8,
                'status' => 'active',
                'featured' => true,
                'module' => 'quiz',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Emily Davis',
                'email' => '<EMAIL>',
                'company' => 'Marketing Pro',
                'position' => 'Freelancer',
                'content' => 'I found amazing job opportunities through JobSpace. The platform connects freelancers with quality clients.',
                'rating' => 4.9,
                'status' => 'active',
                'featured' => true,
                'module' => 'freelancing',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($testimonials as $testimonial) {
            $existing = $this->db->fetch('SELECT id FROM testimonials WHERE email = ?', [$testimonial['email']]);
            
            if (!$existing) {
                $this->db->insert('testimonials', $testimonial);
            }
        }
    }

    /**
     * Seed sample statistics
     */
    private function seedSampleStatistics(): void
    {
        $statistics = [
            ['metric_name' => 'total_users', 'metric_value' => 1250, 'module' => 'general'],
            ['metric_name' => 'total_quizzes', 'metric_value' => 45, 'module' => 'quiz'],
            ['metric_name' => 'total_jobs', 'metric_value' => 128, 'module' => 'freelancing'],
            ['metric_name' => 'total_products', 'metric_value' => 89, 'module' => 'ecommerce'],
            ['metric_name' => 'total_posts', 'metric_value' => 567, 'module' => 'social'],
            ['metric_name' => 'quiz_attempts', 'metric_value' => 3420, 'module' => 'quiz'],
            ['metric_name' => 'successful_hires', 'metric_value' => 67, 'module' => 'freelancing'],
            ['metric_name' => 'orders_completed', 'metric_value' => 234, 'module' => 'ecommerce'],
            ['metric_name' => 'social_interactions', 'metric_value' => 8945, 'module' => 'social']
        ];

        foreach ($statistics as $stat) {
            $stat['date_recorded'] = date('Y-m-d H:i:s');
            
            $existing = $this->db->fetch(
                'SELECT id FROM statistics WHERE metric_name = ? AND module = ?', 
                [$stat['metric_name'], $stat['module']]
            );
            
            if (!$existing) {
                $this->db->insert('statistics', $stat);
            }
        }
    }
}
