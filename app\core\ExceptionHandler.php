<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Request;
use App\Core\Response;
use App\Core\Logger;
use Throwable;
use ErrorException;

/**
 * Exception Handler
 * Handles application exceptions and errors with logging and user-friendly responses
 * 
 * @package App\Core
 */
class ExceptionHandler
{
    private bool $debug;
    private Logger $logger;

    public function __construct()
    {
        $this->debug = (bool) ($_ENV['APP_DEBUG'] ?? false);
        $this->logger = new Logger();
    }

    /**
     * Register exception and error handlers
     */
    public function register(): void
    {
        set_exception_handler([$this, 'handleException']);
        set_error_handler([$this, 'handleError']);
        register_shutdown_function([$this, 'handleShutdown']);
    }

    /**
     * Handle exceptions
     */
    public function handleException(Throwable $e): void
    {
        $this->logException($e);
        
        $response = $this->handle($e, request());
        $response->send();
        
        exit(1);
    }

    /**
     * Handle errors and convert to exceptions
     */
    public function handleError(int $level, string $message, string $file = '', int $line = 0): bool
    {
        if (error_reporting() & $level) {
            throw new ErrorException($message, 0, $level, $file, $line);
        }
        
        return false;
    }

    /**
     * Handle fatal errors on shutdown
     */
    public function handleShutdown(): void
    {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            $this->handleException(
                new ErrorException(
                    $error['message'],
                    0,
                    $error['type'],
                    $error['file'],
                    $error['line']
                )
            );
        }
    }

    /**
     * Handle exception and return response
     */
    public function handle(Throwable $e, Request $request): Response
    {
        // Log the exception
        $this->logException($e);

        // Determine response format
        if ($request->expectsJson()) {
            return $this->handleJsonResponse($e);
        }

        return $this->handleHtmlResponse($e);
    }

    /**
     * Handle JSON response for API requests
     */
    private function handleJsonResponse(Throwable $e): Response
    {
        $statusCode = $this->getStatusCode($e);
        
        $data = [
            'error' => true,
            'message' => $this->getErrorMessage($e),
            'status_code' => $statusCode
        ];

        if ($this->debug) {
            $data['debug'] = [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];
        }

        return Response::json($data, $statusCode);
    }

    /**
     * Handle HTML response for web requests
     */
    private function handleHtmlResponse(Throwable $e): Response
    {
        $statusCode = $this->getStatusCode($e);
        
        if ($this->debug) {
            return $this->renderDebugPage($e, $statusCode);
        }

        return $this->renderErrorPage($statusCode);
    }

    /**
     * Render debug page with full exception details
     */
    private function renderDebugPage(Throwable $e, int $statusCode): Response
    {
        $content = $this->generateDebugHtml($e, $statusCode);
        return new Response($content, $statusCode);
    }

    /**
     * Render user-friendly error page
     */
    private function renderErrorPage(int $statusCode): Response
    {
        $errorPages = [
            404 => 'errors/404.php',
            403 => 'errors/403.php',
            500 => 'errors/500.php'
        ];

        $errorPage = $errorPages[$statusCode] ?? 'errors/500.php';
        $errorPagePath = APP_PATH . '/resources/views/' . $errorPage;

        if (file_exists($errorPagePath)) {
            ob_start();
            include $errorPagePath;
            $content = ob_get_clean();
        } else {
            $content = $this->getDefaultErrorHtml($statusCode);
        }

        return new Response($content, $statusCode);
    }

    /**
     * Generate debug HTML
     */
    private function generateDebugHtml(Throwable $e, int $statusCode): string
    {
        $trace = $this->formatTrace($e->getTrace());
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <title>Error {$statusCode}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .container { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .error-header { background: #dc3545; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 5px 5px 0 0; }
                .error-message { font-size: 18px; margin-bottom: 20px; }
                .error-details { background: #f8f9fa; padding: 15px; border-left: 4px solid #dc3545; margin-bottom: 20px; }
                .trace { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; }
                .trace-item { margin-bottom: 10px; padding: 10px; background: white; border-radius: 3px; }
                .file-path { color: #6c757d; font-size: 14px; }
                pre { margin: 0; white-space: pre-wrap; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='error-header'>
                    <h1>Error {$statusCode}: " . get_class($e) . "</h1>
                </div>
                
                <div class='error-message'>
                    <strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "
                </div>
                
                <div class='error-details'>
                    <strong>File:</strong> " . htmlspecialchars($e->getFile()) . "<br>
                    <strong>Line:</strong> " . $e->getLine() . "
                </div>
                
                <h3>Stack Trace</h3>
                <div class='trace'>
                    {$trace}
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Format exception trace for display
     */
    private function formatTrace(array $trace): string
    {
        $html = '';
        
        foreach ($trace as $index => $item) {
            $file = $item['file'] ?? 'Unknown';
            $line = $item['line'] ?? 0;
            $class = $item['class'] ?? '';
            $function = $item['function'] ?? '';
            $type = $item['type'] ?? '';
            
            $html .= "<div class='trace-item'>";
            $html .= "<strong>#{$index}</strong> ";
            
            if ($class) {
                $html .= htmlspecialchars($class . $type . $function) . "()";
            } else {
                $html .= htmlspecialchars($function) . "()";
            }
            
            $html .= "<div class='file-path'>" . htmlspecialchars($file) . ":" . $line . "</div>";
            $html .= "</div>";
        }
        
        return $html;
    }

    /**
     * Get default error HTML
     */
    private function getDefaultErrorHtml(int $statusCode): string
    {
        $messages = [
            404 => 'Page Not Found',
            403 => 'Access Forbidden',
            500 => 'Internal Server Error'
        ];

        $message = $messages[$statusCode] ?? 'An Error Occurred';

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <title>Error {$statusCode}</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; background: #f5f5f5; }
                .error-container { background: white; display: inline-block; padding: 40px; border-radius: 10px; box-shadow: 0 2px 20px rgba(0,0,0,0.1); }
                h1 { color: #dc3545; font-size: 72px; margin: 0; }
                h2 { color: #6c757d; margin: 10px 0; }
                p { color: #6c757d; }
                a { color: #007bff; text-decoration: none; }
                a:hover { text-decoration: underline; }
            </style>
        </head>
        <body>
            <div class='error-container'>
                <h1>{$statusCode}</h1>
                <h2>{$message}</h2>
                <p>Sorry, something went wrong.</p>
                <a href='/'>Go back to homepage</a>
            </div>
        </body>
        </html>";
    }

    /**
     * Get HTTP status code from exception
     */
    private function getStatusCode(Throwable $e): int
    {
        if (method_exists($e, 'getStatusCode')) {
            return $e->getStatusCode();
        }

        // Map common exceptions to status codes
        $exceptionMap = [
            'InvalidArgumentException' => 400,
            'UnauthorizedException' => 401,
            'ForbiddenException' => 403,
            'NotFoundException' => 404,
            'MethodNotAllowedException' => 405,
            'ValidationException' => 422,
            'TooManyRequestsException' => 429,
        ];

        $exceptionClass = get_class($e);
        $shortClass = substr($exceptionClass, strrpos($exceptionClass, '\\') + 1);

        return $exceptionMap[$shortClass] ?? 500;
    }

    /**
     * Get user-friendly error message
     */
    private function getErrorMessage(Throwable $e): string
    {
        if (!$this->debug) {
            $statusCode = $this->getStatusCode($e);
            
            $messages = [
                400 => 'Bad Request',
                401 => 'Unauthorized',
                403 => 'Forbidden',
                404 => 'Not Found',
                405 => 'Method Not Allowed',
                422 => 'Validation Error',
                429 => 'Too Many Requests',
                500 => 'Internal Server Error'
            ];

            return $messages[$statusCode] ?? 'An error occurred';
        }

        return $e->getMessage();
    }

    /**
     * Log exception
     */
    private function logException(Throwable $e): void
    {
        $context = [
            'exception' => get_class($e),
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];

        $this->logger->error('Exception occurred', $context);
    }
}
