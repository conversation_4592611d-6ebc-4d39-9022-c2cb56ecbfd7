<!-- Stats Section -->
<section class="bg-gray-800 text-white py-16">
    <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-4">Performance Stats</h2>
            <p class="text-gray-300">Built for scale and performance</p>
        </div>
        
        <div class="grid md:grid-cols-4 gap-8 text-center">
            <?php if (isset($stats)): ?>
                <?php foreach ($stats as $stat): ?>
                    <div>
                        <div class="text-4xl font-bold text-<?= $stat['color'] ?>-400 mb-2"><?= htmlspecialchars($stat['value']) ?></div>
                        <p class="text-gray-300"><?= htmlspecialchars($stat['label']) ?></p>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Fallback static content -->
                <div>
                    <div class="text-4xl font-bold text-blue-400 mb-2">50K+</div>
                    <p class="text-gray-300">Concurrent Users</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-green-400 mb-2">&lt;200ms</div>
                    <p class="text-gray-300">Response Time</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-purple-400 mb-2">99.9%</div>
                    <p class="text-gray-300">Uptime</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-yellow-400 mb-2">1M+</div>
                    <p class="text-gray-300">Users Capacity</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
