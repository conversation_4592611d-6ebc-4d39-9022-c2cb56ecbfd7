<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Database;

/**
 * Database Migration System
 * Handles database schema migrations with version control
 * 
 * @package App\Core
 */
class Migration
{
    private Database $db;
    private string $migrationsPath;
    private string $migrationsTable = 'migrations';

    public function __construct()
    {
        $this->db = app(Database::class);
        $this->migrationsPath = ROOT_PATH . '/database/migrations';
        $this->ensureMigrationsTable();
    }

    /**
     * Ensure migrations table exists
     */
    private function ensureMigrationsTable(): void
    {
        $sql = "CREATE TABLE IF NOT EXISTS {$this->migrationsTable} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration VARCHAR(255) NOT NULL,
            batch INT NOT NULL,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_migration (migration)
        ) ENGINE=InnoDB";

        $this->db->execute($sql);
    }

    /**
     * Run pending migrations
     */
    public function migrate(): array
    {
        $pendingMigrations = $this->getPendingMigrations();
        
        if (empty($pendingMigrations)) {
            return ['message' => 'No pending migrations'];
        }

        $batch = $this->getNextBatchNumber();
        $executed = [];

        $this->db->beginTransaction();

        try {
            foreach ($pendingMigrations as $migration) {
                $this->executeMigration($migration, $batch);
                $executed[] = $migration;
            }

            $this->db->commit();

            return [
                'message' => 'Migrations executed successfully',
                'executed' => $executed,
                'batch' => $batch
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception("Migration failed: " . $e->getMessage());
        }
    }

    /**
     * Rollback last batch of migrations
     */
    public function rollback(): array
    {
        $lastBatch = $this->getLastBatchNumber();
        
        if (!$lastBatch) {
            return ['message' => 'No migrations to rollback'];
        }

        $migrations = $this->getMigrationsByBatch($lastBatch);
        $rolledBack = [];

        $this->db->beginTransaction();

        try {
            // Execute rollbacks in reverse order
            foreach (array_reverse($migrations) as $migration) {
                $this->rollbackMigration($migration);
                $rolledBack[] = $migration;
            }

            $this->db->commit();

            return [
                'message' => 'Rollback completed successfully',
                'rolled_back' => $rolledBack,
                'batch' => $lastBatch
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception("Rollback failed: " . $e->getMessage());
        }
    }

    /**
     * Get migration status
     */
    public function status(): array
    {
        $allMigrations = $this->getAllMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();
        
        $status = [];
        
        foreach ($allMigrations as $migration) {
            $status[] = [
                'migration' => $migration,
                'status' => in_array($migration, $executedMigrations) ? 'executed' : 'pending'
            ];
        }

        return $status;
    }

    /**
     * Get pending migrations
     */
    private function getPendingMigrations(): array
    {
        $allMigrations = $this->getAllMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();
        
        return array_diff($allMigrations, $executedMigrations);
    }

    /**
     * Get all migration files
     */
    private function getAllMigrationFiles(): array
    {
        if (!is_dir($this->migrationsPath)) {
            mkdir($this->migrationsPath, 0755, true);
            return [];
        }

        $files = glob($this->migrationsPath . '/*.php');
        $migrations = [];

        foreach ($files as $file) {
            $migrations[] = basename($file, '.php');
        }

        sort($migrations);
        return $migrations;
    }

    /**
     * Get executed migrations
     */
    private function getExecutedMigrations(): array
    {
        $result = $this->db->fetchAll(
            "SELECT migration FROM {$this->migrationsTable} ORDER BY migration"
        );

        return array_column($result, 'migration');
    }

    /**
     * Get next batch number
     */
    private function getNextBatchNumber(): int
    {
        $lastBatch = $this->db->fetchColumn(
            "SELECT MAX(batch) FROM {$this->migrationsTable}"
        );

        return ((int) $lastBatch) + 1;
    }

    /**
     * Get last batch number
     */
    private function getLastBatchNumber(): ?int
    {
        $lastBatch = $this->db->fetchColumn(
            "SELECT MAX(batch) FROM {$this->migrationsTable}"
        );

        return $lastBatch ? (int) $lastBatch : null;
    }

    /**
     * Get migrations by batch
     */
    private function getMigrationsByBatch(int $batch): array
    {
        $result = $this->db->fetchAll(
            "SELECT migration FROM {$this->migrationsTable} WHERE batch = ? ORDER BY migration",
            [$batch]
        );

        return array_column($result, 'migration');
    }

    /**
     * Execute a migration
     */
    private function executeMigration(string $migration, int $batch): void
    {
        $migrationFile = $this->migrationsPath . '/' . $migration . '.php';
        
        if (!file_exists($migrationFile)) {
            throw new \Exception("Migration file not found: {$migration}");
        }

        // Include migration file
        $migrationClass = require $migrationFile;
        
        if (!is_object($migrationClass) || !method_exists($migrationClass, 'up')) {
            throw new \Exception("Invalid migration file: {$migration}");
        }

        // Execute migration
        $migrationClass->up($this->db);

        // Record migration
        $this->db->insert($this->migrationsTable, [
            'migration' => $migration,
            'batch' => $batch
        ]);
    }

    /**
     * Rollback a migration
     */
    private function rollbackMigration(string $migration): void
    {
        $migrationFile = $this->migrationsPath . '/' . $migration . '.php';
        
        if (!file_exists($migrationFile)) {
            throw new \Exception("Migration file not found: {$migration}");
        }

        // Include migration file
        $migrationClass = require $migrationFile;
        
        if (!is_object($migrationClass) || !method_exists($migrationClass, 'down')) {
            throw new \Exception("Invalid migration file or missing down method: {$migration}");
        }

        // Execute rollback
        $migrationClass->down($this->db);

        // Remove migration record
        $this->db->delete($this->migrationsTable, ['migration' => $migration]);
    }

    /**
     * Create a new migration file
     */
    public function create(string $name): string
    {
        $timestamp = date('Y_m_d_His');
        $className = $this->studlyCase($name);
        $filename = "{$timestamp}_{$name}.php";
        $filepath = $this->migrationsPath . '/' . $filename;

        if (!is_dir($this->migrationsPath)) {
            mkdir($this->migrationsPath, 0755, true);
        }

        $template = $this->getMigrationTemplate($className);
        file_put_contents($filepath, $template);

        return $filename;
    }

    /**
     * Get migration template
     */
    private function getMigrationTemplate(string $className): string
    {
        return "<?php

use App\Core\Database;

return new class {
    /**
     * Run the migration
     */
    public function up(Database \$db): void
    {
        // Add your migration logic here
        \$sql = \"
            CREATE TABLE example_table (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB
        \";
        
        \$db->execute(\$sql);
    }

    /**
     * Reverse the migration
     */
    public function down(Database \$db): void
    {
        // Add your rollback logic here
        \$db->execute(\"DROP TABLE IF EXISTS example_table\");
    }
};
";
    }

    /**
     * Convert string to StudlyCase
     */
    private function studlyCase(string $value): string
    {
        return str_replace(' ', '', ucwords(str_replace(['_', '-'], ' ', $value)));
    }

    /**
     * Reset all migrations (dangerous!)
     */
    public function reset(): array
    {
        $executedMigrations = $this->getExecutedMigrations();
        
        if (empty($executedMigrations)) {
            return ['message' => 'No migrations to reset'];
        }

        $this->db->beginTransaction();

        try {
            // Get all migrations in reverse order
            $migrations = $this->db->fetchAll(
                "SELECT migration FROM {$this->migrationsTable} ORDER BY batch DESC, migration DESC"
            );

            $reset = [];
            foreach ($migrations as $migration) {
                $this->rollbackMigration($migration['migration']);
                $reset[] = $migration['migration'];
            }

            $this->db->commit();

            return [
                'message' => 'All migrations reset successfully',
                'reset' => $reset
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw new \Exception("Reset failed: " . $e->getMessage());
        }
    }
}
