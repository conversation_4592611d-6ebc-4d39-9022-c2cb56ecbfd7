<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Database;
use App\Core\Session;

/**
 * Authentication Manager
 * Handles user authentication and authorization with multi-role support
 * 
 * @package App\Core
 */
class Auth
{
    private Database $db;
    private ?array $user = null;
    private array $roles = ['admin', 'creator', 'business', 'user'];

    public function __construct(Database $db)
    {
        $this->db = $db;
        $this->loadUser();
    }

    /**
     * Load authenticated user from session
     */
    private function loadUser(): void
    {
        $userId = session('user_id');
        
        if ($userId) {
            $this->user = $this->db->fetch(
                "SELECT u.*, ur.role_name 
                 FROM users u 
                 LEFT JOIN user_roles ur ON u.id = ur.user_id 
                 WHERE u.id = ? AND u.status = 'active'",
                [$userId]
            );
        }
    }

    /**
     * Attempt to authenticate user
     */
    public function attempt(array $credentials): bool
    {
        $email = $credentials['email'] ?? '';
        $password = $credentials['password'] ?? '';
        $remember = $credentials['remember'] ?? false;

        if (empty($email) || empty($password)) {
            return false;
        }

        // Find user by email
        $user = $this->db->fetch(
            "SELECT u.*, ur.role_name 
             FROM users u 
             LEFT JOIN user_roles ur ON u.id = ur.user_id 
             WHERE u.email = ? AND u.status = 'active'",
            [$email]
        );

        if (!$user) {
            return false;
        }

        // Verify password
        if (!password_verify($password, $user['password'])) {
            // Log failed attempt
            $this->logFailedAttempt($email);
            return false;
        }

        // Check if account is locked
        if ($this->isAccountLocked($user['id'])) {
            return false;
        }

        // Login successful
        $this->login($user, $remember);
        $this->clearFailedAttempts($email);
        
        return true;
    }

    /**
     * Login user
     */
    public function login(array $user, bool $remember = false): void
    {
        $this->user = $user;
        
        // Store user ID in session
        session('user_id', $user['id']);
        
        // Regenerate session ID for security
        session()->regenerateId();
        
        // Update last login
        $this->db->update('users', [
            'last_login_at' => now(),
            'last_login_ip' => request()->ip()
        ], ['id' => $user['id']]);

        // Handle remember me
        if ($remember) {
            $this->setRememberToken($user['id']);
        }

        // Log successful login
        $this->logActivity('login', $user['id']);
    }

    /**
     * Logout user
     */
    public function logout(): void
    {
        if ($this->user) {
            // Log logout activity
            $this->logActivity('logout', $this->user['id']);
            
            // Clear remember token
            $this->clearRememberToken($this->user['id']);
        }

        // Clear session
        session()->remove('user_id');
        session()->invalidate();
        
        $this->user = null;
    }

    /**
     * Check if user is authenticated
     */
    public function check(): bool
    {
        return $this->user !== null;
    }

    /**
     * Check if user is guest (not authenticated)
     */
    public function guest(): bool
    {
        return !$this->check();
    }

    /**
     * Get authenticated user
     */
    public function user(): ?array
    {
        return $this->user;
    }

    /**
     * Get user ID
     */
    public function id(): ?int
    {
        return $this->user['id'] ?? null;
    }

    /**
     * Check if user has specific role
     */
    public function hasRole(string $role): bool
    {
        if (!$this->check()) {
            return false;
        }

        return $this->user['role_name'] === $role;
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(array $roles): bool
    {
        if (!$this->check()) {
            return false;
        }

        return in_array($this->user['role_name'], $roles);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is creator
     */
    public function isCreator(): bool
    {
        return $this->hasRole('creator');
    }

    /**
     * Check if user is business
     */
    public function isBusiness(): bool
    {
        return $this->hasRole('business');
    }

    /**
     * Check if user is regular user
     */
    public function isUser(): bool
    {
        return $this->hasRole('user');
    }

    /**
     * Register new user
     */
    public function register(array $userData): ?int
    {
        // Hash password
        $userData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
        $userData['created_at'] = now();
        $userData['status'] = 'pending'; // Requires email verification
        
        // Generate verification token
        $userData['email_verification_token'] = bin2hex(random_bytes(32));

        try {
            $this->db->beginTransaction();

            // Insert user
            $userId = $this->db->insert('users', $userData);

            // Assign default role
            $defaultRole = $userData['role'] ?? 'user';
            $this->db->insert('user_roles', [
                'user_id' => $userId,
                'role_name' => $defaultRole,
                'created_at' => now()
            ]);

            $this->db->commit();
            return $userId;

        } catch (\Exception $e) {
            $this->db->rollback();
            return null;
        }
    }

    /**
     * Verify email address
     */
    public function verifyEmail(string $token): bool
    {
        $user = $this->db->fetch(
            "SELECT id FROM users WHERE email_verification_token = ? AND status = 'pending'",
            [$token]
        );

        if (!$user) {
            return false;
        }

        return $this->db->update('users', [
            'status' => 'active',
            'email_verified_at' => now(),
            'email_verification_token' => null
        ], ['id' => $user['id']]) > 0;
    }

    /**
     * Set remember token
     */
    private function setRememberToken(int $userId): void
    {
        $token = bin2hex(random_bytes(32));
        $hashedToken = hash('sha256', $token);
        
        // Store hashed token in database
        $this->db->insert('user_remember_tokens', [
            'user_id' => $userId,
            'token' => $hashedToken,
            'expires_at' => date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60)), // 30 days
            'created_at' => now()
        ]);

        // Set cookie
        setcookie(
            'remember_token',
            $token,
            time() + (30 * 24 * 60 * 60), // 30 days
            '/',
            '',
            request()->isSecure(),
            true
        );
    }

    /**
     * Clear remember token
     */
    private function clearRememberToken(int $userId): void
    {
        $this->db->delete('user_remember_tokens', ['user_id' => $userId]);
        setcookie('remember_token', '', time() - 3600, '/');
    }

    /**
     * Log failed login attempt
     */
    private function logFailedAttempt(string $email): void
    {
        $this->db->insert('failed_login_attempts', [
            'email' => $email,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'attempted_at' => now()
        ]);
    }

    /**
     * Clear failed login attempts
     */
    private function clearFailedAttempts(string $email): void
    {
        $this->db->delete('failed_login_attempts', ['email' => $email]);
    }

    /**
     * Check if account is locked
     */
    private function isAccountLocked(int $userId): bool
    {
        $attempts = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM failed_login_attempts 
             WHERE email = (SELECT email FROM users WHERE id = ?) 
             AND attempted_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)",
            [$userId]
        );

        return $attempts >= 5; // Lock after 5 failed attempts in 15 minutes
    }

    /**
     * Log user activity
     */
    private function logActivity(string $action, int $userId): void
    {
        $this->db->insert('user_activity_logs', [
            'user_id' => $userId,
            'action' => $action,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'created_at' => now()
        ]);
    }
}
