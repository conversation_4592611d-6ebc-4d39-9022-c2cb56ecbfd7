<?php

declare(strict_types=1);

namespace App\Core;

use Closure;
use Exception;
use ReflectionClass;
use ReflectionParameter;

/**
 * Dependency Injection Container
 * Manages service registration and resolution
 * 
 * @package App\Core
 */
class Container
{
    private array $bindings = [];
    private array $instances = [];
    private array $singletons = [];

    /**
     * Bind a service to the container
     */
    public function bind(string $abstract, $concrete = null, bool $shared = false): void
    {
        if ($concrete === null) {
            $concrete = $abstract;
        }

        $this->bindings[$abstract] = [
            'concrete' => $concrete,
            'shared' => $shared
        ];
    }

    /**
     * Bind a singleton service to the container
     */
    public function singleton(string $abstract, $concrete = null): void
    {
        $this->bind($abstract, $concrete, true);
    }

    /**
     * Register an existing instance as shared
     */
    public function instance(string $abstract, $instance): void
    {
        $this->instances[$abstract] = $instance;
    }

    /**
     * Resolve a service from the container
     */
    public function get(string $abstract)
    {
        // Return existing instance if available
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        // Check if binding exists
        if (!isset($this->bindings[$abstract])) {
            // Try to auto-resolve if class exists
            if (class_exists($abstract)) {
                return $this->resolve($abstract);
            }
            
            throw new Exception("Service [{$abstract}] not found in container");
        }

        $binding = $this->bindings[$abstract];
        $concrete = $binding['concrete'];

        // Resolve the service
        $object = $this->resolve($concrete);

        // Store as singleton if needed
        if ($binding['shared']) {
            $this->instances[$abstract] = $object;
        }

        return $object;
    }

    /**
     * Resolve a concrete service
     */
    private function resolve($concrete)
    {
        // If concrete is a closure, call it
        if ($concrete instanceof Closure) {
            return $concrete($this);
        }

        // If concrete is a string, resolve as class
        if (is_string($concrete)) {
            return $this->resolveClass($concrete);
        }

        // Return as-is for other types
        return $concrete;
    }

    /**
     * Resolve a class with its dependencies
     */
    private function resolveClass(string $className)
    {
        $reflectionClass = new ReflectionClass($className);

        // Check if class is instantiable
        if (!$reflectionClass->isInstantiable()) {
            throw new Exception("Class [{$className}] is not instantiable");
        }

        $constructor = $reflectionClass->getConstructor();

        // If no constructor, create instance directly
        if ($constructor === null) {
            return new $className();
        }

        // Resolve constructor dependencies
        $dependencies = $this->resolveDependencies($constructor->getParameters());

        return $reflectionClass->newInstanceArgs($dependencies);
    }

    /**
     * Resolve method dependencies
     */
    private function resolveDependencies(array $parameters): array
    {
        $dependencies = [];

        foreach ($parameters as $parameter) {
            $dependency = $this->resolveDependency($parameter);
            $dependencies[] = $dependency;
        }

        return $dependencies;
    }

    /**
     * Resolve a single dependency
     */
    private function resolveDependency(ReflectionParameter $parameter)
    {
        $type = $parameter->getType();

        // Handle union types (PHP 8+)
        if ($type && method_exists($type, 'getName')) {
            $typeName = $type->getName();
            
            // Try to resolve from container
            if (class_exists($typeName) || interface_exists($typeName)) {
                return $this->get($typeName);
            }
        }

        // Check if parameter has default value
        if ($parameter->isDefaultValueAvailable()) {
            return $parameter->getDefaultValue();
        }

        // Check if parameter is nullable
        if ($parameter->allowsNull()) {
            return null;
        }

        throw new Exception("Cannot resolve dependency [{$parameter->getName()}]");
    }

    /**
     * Check if service is bound
     */
    public function bound(string $abstract): bool
    {
        return isset($this->bindings[$abstract]) || isset($this->instances[$abstract]);
    }

    /**
     * Remove a binding
     */
    public function forget(string $abstract): void
    {
        unset($this->bindings[$abstract], $this->instances[$abstract]);
    }

    /**
     * Get all bindings
     */
    public function getBindings(): array
    {
        return $this->bindings;
    }

    /**
     * Call a method with dependency injection
     */
    public function call($callback, array $parameters = [])
    {
        if (is_array($callback)) {
            [$class, $method] = $callback;
            
            if (is_string($class)) {
                $class = $this->get($class);
            }
            
            $reflectionMethod = new \ReflectionMethod($class, $method);
            $dependencies = $this->resolveDependencies($reflectionMethod->getParameters());
            
            // Merge provided parameters
            $dependencies = array_merge($dependencies, $parameters);
            
            return $reflectionMethod->invokeArgs($class, $dependencies);
        }

        if ($callback instanceof Closure) {
            $reflectionFunction = new \ReflectionFunction($callback);
            $dependencies = $this->resolveDependencies($reflectionFunction->getParameters());
            
            // Merge provided parameters
            $dependencies = array_merge($dependencies, $parameters);
            
            return $reflectionFunction->invokeArgs($dependencies);
        }

        throw new Exception("Invalid callback provided");
    }

    /**
     * Flush all bindings and instances
     */
    public function flush(): void
    {
        $this->bindings = [];
        $this->instances = [];
        $this->singletons = [];
    }
}
