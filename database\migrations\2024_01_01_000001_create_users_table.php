<?php

use App\Core\Database;

return new class {
    /**
     * Run the migration
     */
    public function up(Database $db): void
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'creator', 'business', 'user') DEFAULT 'user',
                status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
                email_verified_at TIMESTAMP NULL,
                email_verification_token VARCHAR(255) NULL,
                last_login_at TIMESTAMP NULL,
                last_login_ip VARCHAR(45) NULL,
                avatar VARCHAR(255) NULL,
                bio TEXT NULL,
                phone VARCHAR(20) NULL,
                address TEXT NULL,
                city VARCHAR(100) NULL,
                country VARCHAR(100) NULL,
                timezone VARCHAR(50) DEFAULT 'UTC',
                language VARCHAR(10) DEFAULT 'en',
                preferences JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_status (status),
                INDEX idx_role (role),
                INDEX idx_email_verified (email_verified_at),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
        ";
        
        $db->execute($sql);
    }

    /**
     * Reverse the migration
     */
    public function down(Database $db): void
    {
        $db->execute("DROP TABLE IF EXISTS users");
    }
};
