{"name": "auth", "version": "1.0.0", "description": "Authentication and authorization module with multi-role support", "enabled": true, "dependencies": [], "features": ["login", "register", "two_step_verification", "email_verification", "password_reset", "social_login", "role_management", "profile_management", "security_settings", "activity_logs", "session_management", "two_factor_auth", "account_recovery", "password_policy", "login_attempts"], "routes": {"prefix": "auth", "middleware": ["csrf"]}, "permissions": {"public": true, "roles": ["guest", "user", "creator", "business", "admin"]}, "settings": {"registration_enabled": true, "email_verification_required": true, "two_factor_enabled": false, "social_login_enabled": true, "password_reset_enabled": true, "max_login_attempts": 5, "lockout_duration": 900}}