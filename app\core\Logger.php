<?php

declare(strict_types=1);

namespace App\Core;

/**
 * Application Logger
 * Handles logging with multiple levels and file rotation
 * 
 * @package App\Core
 */
class Logger
{
    private string $logPath;
    private string $logLevel;
    private int $maxFileSize;
    private int $maxFiles;

    const EMERGENCY = 'emergency';
    const ALERT = 'alert';
    const CRITICAL = 'critical';
    const ERROR = 'error';
    const WARNING = 'warning';
    const NOTICE = 'notice';
    const INFO = 'info';
    const DEBUG = 'debug';

    private array $levels = [
        self::EMERGENCY => 0,
        self::ALERT => 1,
        self::CRITICAL => 2,
        self::ERROR => 3,
        self::WARNING => 4,
        self::NOTICE => 5,
        self::INFO => 6,
        self::DEBUG => 7,
    ];

    public function __construct()
    {
        $this->logPath = LOGS_PATH;
        $this->logLevel = $_ENV['LOG_LEVEL'] ?? self::INFO;
        $this->maxFileSize = (int) ($_ENV['LOG_MAX_FILE_SIZE'] ?? 10485760); // 10MB
        $this->maxFiles = (int) ($_ENV['LOG_MAX_FILES'] ?? 5);

        // Ensure log directory exists
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }

    /**
     * Log emergency message
     */
    public function emergency(string $message, array $context = []): void
    {
        $this->log(self::EMERGENCY, $message, $context);
    }

    /**
     * Log alert message
     */
    public function alert(string $message, array $context = []): void
    {
        $this->log(self::ALERT, $message, $context);
    }

    /**
     * Log critical message
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log(self::CRITICAL, $message, $context);
    }

    /**
     * Log error message
     */
    public function error(string $message, array $context = []): void
    {
        $this->log(self::ERROR, $message, $context);
    }

    /**
     * Log warning message
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log(self::WARNING, $message, $context);
    }

    /**
     * Log notice message
     */
    public function notice(string $message, array $context = []): void
    {
        $this->log(self::NOTICE, $message, $context);
    }

    /**
     * Log info message
     */
    public function info(string $message, array $context = []): void
    {
        $this->log(self::INFO, $message, $context);
    }

    /**
     * Log debug message
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log(self::DEBUG, $message, $context);
    }

    /**
     * Log message with specified level
     */
    public function log(string $level, string $message, array $context = []): void
    {
        // Check if level should be logged
        if (!$this->shouldLog($level)) {
            return;
        }

        $logEntry = $this->formatLogEntry($level, $message, $context);
        $logFile = $this->getLogFile($level);

        // Check if log rotation is needed
        if (file_exists($logFile) && filesize($logFile) > $this->maxFileSize) {
            $this->rotateLogFile($logFile);
        }

        // Write log entry
        file_put_contents($logFile, $logEntry . PHP_EOL, FILE_APPEND | LOCK_EX);
    }

    /**
     * Check if level should be logged
     */
    private function shouldLog(string $level): bool
    {
        $currentLevelValue = $this->levels[$this->logLevel] ?? 7;
        $messageLevelValue = $this->levels[$level] ?? 7;

        return $messageLevelValue <= $currentLevelValue;
    }

    /**
     * Format log entry
     */
    private function formatLogEntry(string $level, string $message, array $context): string
    {
        $timestamp = date('Y-m-d H:i:s');
        $levelUpper = strtoupper($level);
        
        // Interpolate context into message
        $message = $this->interpolate($message, $context);
        
        // Add context as JSON if not empty
        $contextString = '';
        if (!empty($context)) {
            $contextString = ' ' . json_encode($context, JSON_UNESCAPED_SLASHES);
        }

        return "[{$timestamp}] {$levelUpper}: {$message}{$contextString}";
    }

    /**
     * Interpolate context values into message placeholders
     */
    private function interpolate(string $message, array $context): string
    {
        $replace = [];
        
        foreach ($context as $key => $value) {
            if (is_string($value) || is_numeric($value) || (is_object($value) && method_exists($value, '__toString'))) {
                $replace['{' . $key . '}'] = $value;
            }
        }

        return strtr($message, $replace);
    }

    /**
     * Get log file path for level
     */
    private function getLogFile(string $level): string
    {
        $date = date('Y-m-d');
        return $this->logPath . "/{$level}-{$date}.log";
    }

    /**
     * Rotate log file when it gets too large
     */
    private function rotateLogFile(string $logFile): void
    {
        $pathInfo = pathinfo($logFile);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'];
        $directory = $pathInfo['dirname'];

        // Rotate existing files
        for ($i = $this->maxFiles - 1; $i > 0; $i--) {
            $oldFile = "{$directory}/{$baseName}.{$i}.{$extension}";
            $newFile = "{$directory}/{$baseName}." . ($i + 1) . ".{$extension}";

            if (file_exists($oldFile)) {
                if ($i === $this->maxFiles - 1) {
                    unlink($oldFile); // Delete oldest file
                } else {
                    rename($oldFile, $newFile);
                }
            }
        }

        // Move current file to .1
        $rotatedFile = "{$directory}/{$baseName}.1.{$extension}";
        rename($logFile, $rotatedFile);
    }

    /**
     * Get log entries for a specific level and date
     */
    public function getLogEntries(string $level, string $date = null, int $limit = 100): array
    {
        $date = $date ?: date('Y-m-d');
        $logFile = $this->logPath . "/{$level}-{$date}.log";

        if (!file_exists($logFile)) {
            return [];
        }

        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        if ($limit > 0) {
            $lines = array_slice($lines, -$limit);
        }

        return array_reverse($lines);
    }

    /**
     * Clear logs for a specific level and date
     */
    public function clearLogs(string $level = null, string $date = null): bool
    {
        if ($level && $date) {
            $logFile = $this->logPath . "/{$level}-{$date}.log";
            return file_exists($logFile) ? unlink($logFile) : true;
        }

        if ($level) {
            $pattern = $this->logPath . "/{$level}-*.log";
        } else {
            $pattern = $this->logPath . "/*.log";
        }

        $files = glob($pattern);
        $success = true;

        foreach ($files as $file) {
            if (!unlink($file)) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Get log file size
     */
    public function getLogFileSize(string $level, string $date = null): int
    {
        $date = $date ?: date('Y-m-d');
        $logFile = $this->logPath . "/{$level}-{$date}.log";

        return file_exists($logFile) ? filesize($logFile) : 0;
    }

    /**
     * Get all log files
     */
    public function getLogFiles(): array
    {
        $files = glob($this->logPath . '/*.log');
        $logFiles = [];

        foreach ($files as $file) {
            $fileName = basename($file);
            $parts = explode('-', $fileName);
            
            if (count($parts) >= 2) {
                $level = $parts[0];
                $date = str_replace('.log', '', $parts[1]);
                
                $logFiles[] = [
                    'level' => $level,
                    'date' => $date,
                    'file' => $file,
                    'size' => filesize($file),
                    'modified' => filemtime($file)
                ];
            }
        }

        // Sort by modification time (newest first)
        usort($logFiles, function ($a, $b) {
            return $b['modified'] - $a['modified'];
        });

        return $logFiles;
    }

    /**
     * Monitor log file in real-time
     */
    public function tail(string $level, string $date = null, int $lines = 50): array
    {
        $date = $date ?: date('Y-m-d');
        $logFile = $this->logPath . "/{$level}-{$date}.log";

        if (!file_exists($logFile)) {
            return [];
        }

        $command = "tail -n {$lines} " . escapeshellarg($logFile);
        $output = shell_exec($command);

        return $output ? explode("\n", trim($output)) : [];
    }

    /**
     * Search log entries
     */
    public function search(string $query, string $level = null, string $date = null): array
    {
        $results = [];
        $date = $date ?: date('Y-m-d');

        if ($level) {
            $files = [$this->logPath . "/{$level}-{$date}.log"];
        } else {
            $files = glob($this->logPath . "/*-{$date}.log");
        }

        foreach ($files as $file) {
            if (!file_exists($file)) {
                continue;
            }

            $lines = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            
            foreach ($lines as $lineNumber => $line) {
                if (stripos($line, $query) !== false) {
                    $results[] = [
                        'file' => basename($file),
                        'line' => $lineNumber + 1,
                        'content' => $line
                    ];
                }
            }
        }

        return $results;
    }
}
