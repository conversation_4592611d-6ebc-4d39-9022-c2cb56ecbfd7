<?php

declare(strict_types=1);

namespace App\Core;

use App\Core\Container;
use App\Core\Request;
use App\Core\Response;
use Exception;

/**
 * Application Router
 * Handles route registration and dispatching
 * 
 * @package App\Core
 */
class Router
{
    private Container $container;
    private array $routes = [];
    private array $namedRoutes = [];
    private array $middleware = [];
    private string $currentPrefix = '';
    private array $currentMiddleware = [];

    public function __construct(Container $container)
    {
        $this->container = $container;
    }

    /**
     * Register a GET route
     */
    public function get(string $uri, $action, string $name = null): self
    {
        return $this->addRoute('GET', $uri, $action, $name);
    }

    /**
     * Register a POST route
     */
    public function post(string $uri, $action, string $name = null): self
    {
        return $this->addRoute('POST', $uri, $action, $name);
    }

    /**
     * Register a PUT route
     */
    public function put(string $uri, $action, string $name = null): self
    {
        return $this->addRoute('PUT', $uri, $action, $name);
    }

    /**
     * Register a DELETE route
     */
    public function delete(string $uri, $action, string $name = null): self
    {
        return $this->addRoute('DELETE', $uri, $action, $name);
    }

    /**
     * Register a PATCH route
     */
    public function patch(string $uri, $action, string $name = null): self
    {
        return $this->addRoute('PATCH', $uri, $action, $name);
    }

    /**
     * Register routes for multiple HTTP methods
     */
    public function match(array $methods, string $uri, $action, string $name = null): self
    {
        foreach ($methods as $method) {
            $this->addRoute(strtoupper($method), $uri, $action, $name);
        }
        return $this;
    }

    /**
     * Register route for all HTTP methods
     */
    public function any(string $uri, $action, string $name = null): self
    {
        return $this->match(['GET', 'POST', 'PUT', 'DELETE', 'PATCH'], $uri, $action, $name);
    }

    /**
     * Add a route to the collection
     */
    private function addRoute(string $method, string $uri, $action, string $name = null): self
    {
        $uri = $this->currentPrefix . '/' . trim($uri, '/');
        $uri = '/' . trim($uri, '/');
        
        $route = [
            'method' => $method,
            'uri' => $uri,
            'action' => $action,
            'middleware' => $this->currentMiddleware,
            'parameters' => []
        ];

        $this->routes[] = $route;

        // Store named route
        if ($name) {
            $this->namedRoutes[$name] = $route;
        }

        return $this;
    }

    /**
     * Group routes with common attributes
     */
    public function group(array $attributes, callable $callback): void
    {
        $previousPrefix = $this->currentPrefix;
        $previousMiddleware = $this->currentMiddleware;

        // Apply group attributes
        if (isset($attributes['prefix'])) {
            $this->currentPrefix .= '/' . trim($attributes['prefix'], '/');
        }

        if (isset($attributes['middleware'])) {
            $this->currentMiddleware = array_merge(
                $this->currentMiddleware,
                (array) $attributes['middleware']
            );
        }

        // Execute callback
        $callback($this);

        // Restore previous state
        $this->currentPrefix = $previousPrefix;
        $this->currentMiddleware = $previousMiddleware;
    }

    /**
     * Dispatch the request to appropriate route
     */
    public function dispatch(Request $request): Response
    {
        $method = $request->getMethod();
        $uri = $request->getUri();

        foreach ($this->routes as $route) {
            if ($this->matchRoute($route, $method, $uri)) {
                return $this->handleRoute($route, $request);
            }
        }

        // No route found
        return $this->handleNotFound();
    }

    /**
     * Check if route matches the request
     */
    private function matchRoute(array $route, string $method, string $uri): bool
    {
        // Check HTTP method
        if ($route['method'] !== $method) {
            return false;
        }

        // Convert route pattern to regex
        $pattern = $this->convertToRegex($route['uri']);
        
        return preg_match($pattern, $uri, $matches);
    }

    /**
     * Convert route URI to regex pattern
     */
    private function convertToRegex(string $uri): string
    {
        // Escape special regex characters
        $pattern = preg_quote($uri, '/');
        
        // Replace parameter placeholders
        $pattern = preg_replace('/\\\{([^}]+)\\\}/', '([^/]+)', $pattern);
        
        return '/^' . $pattern . '$/';
    }

    /**
     * Handle matched route
     */
    private function handleRoute(array $route, Request $request): Response
    {
        // Extract route parameters
        $parameters = $this->extractParameters($route, $request->getUri());
        $route['parameters'] = $parameters;

        // Apply route middleware
        foreach ($route['middleware'] as $middlewareClass) {
            $middleware = new $middlewareClass();
            $request = $middleware->handle($request);
        }

        // Resolve and call the action
        return $this->callAction($route['action'], $request, $parameters);
    }

    /**
     * Extract parameters from URI
     */
    private function extractParameters(array $route, string $uri): array
    {
        $pattern = $this->convertToRegex($route['uri']);
        preg_match($pattern, $uri, $matches);
        
        // Remove full match
        array_shift($matches);
        
        return $matches;
    }

    /**
     * Call the route action
     */
    private function callAction($action, Request $request, array $parameters): Response
    {
        if (is_string($action)) {
            // Handle Controller@method format
            if (strpos($action, '@') !== false) {
                [$controller, $method] = explode('@', $action);
                $controllerInstance = $this->container->get($controller);
                return $controllerInstance->$method($request, ...$parameters);
            }
            
            // Handle function name
            if (function_exists($action)) {
                return $action($request, ...$parameters);
            }
        }

        if (is_callable($action)) {
            return $this->container->call($action, [$request, ...$parameters]);
        }

        throw new Exception("Invalid route action");
    }

    /**
     * Handle 404 Not Found
     */
    private function handleNotFound(): Response
    {
        $response = new Response();
        $response->setStatusCode(404);
        $response->setContent('Page Not Found');
        return $response;
    }

    /**
     * Generate URL for named route
     */
    public function route(string $name, array $parameters = []): string
    {
        if (!isset($this->namedRoutes[$name])) {
            throw new Exception("Named route [{$name}] not found");
        }

        $route = $this->namedRoutes[$name];
        $uri = $route['uri'];

        // Replace parameters in URI
        foreach ($parameters as $key => $value) {
            $uri = str_replace('{' . $key . '}', $value, $uri);
        }

        return $uri;
    }

    /**
     * Get all registered routes
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }
}
