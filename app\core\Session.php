<?php

declare(strict_types=1);

namespace App\Core;

/**
 * Session Management Class
 * Handles secure session management with enhanced security features
 * 
 * @package App\Core
 */
class Session
{
    private bool $started = false;
    private array $config = [];

    public function __construct()
    {
        $this->loadConfig();
        $this->configureSession();
    }

    /**
     * Load session configuration
     */
    private function loadConfig(): void
    {
        $this->config = [
            'name' => $_ENV['SESSION_NAME'] ?? 'jobspace_session',
            'lifetime' => (int) ($_ENV['SESSION_LIFETIME'] ?? 7200), // 2 hours
            'path' => $_ENV['SESSION_PATH'] ?? '/',
            'domain' => $_ENV['SESSION_DOMAIN'] ?? '',
            'secure' => (bool) ($_ENV['SESSION_SECURE'] ?? false),
            'httponly' => true,
            'samesite' => $_ENV['SESSION_SAMESITE'] ?? 'Lax',
            'save_path' => STORAGE_PATH . '/sessions',
        ];
    }

    /**
     * Configure session settings
     */
    private function configureSession(): void
    {
        // Ensure session directory exists
        if (!is_dir($this->config['save_path'])) {
            mkdir($this->config['save_path'], 0755, true);
        }

        // Set session configuration
        ini_set('session.name', $this->config['name']);
        ini_set('session.gc_maxlifetime', (string) $this->config['lifetime']);
        ini_set('session.cookie_lifetime', (string) $this->config['lifetime']);
        ini_set('session.cookie_path', $this->config['path']);
        ini_set('session.cookie_domain', $this->config['domain']);
        ini_set('session.cookie_secure', $this->config['secure'] ? '1' : '0');
        ini_set('session.cookie_httponly', '1');
        ini_set('session.cookie_samesite', $this->config['samesite']);
        ini_set('session.use_strict_mode', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.save_path', $this->config['save_path']);
        
        // Enhanced security settings
        ini_set('session.entropy_length', '32');
        ini_set('session.hash_function', 'sha256');
        ini_set('session.hash_bits_per_character', '6');
    }

    /**
     * Start the session
     */
    public function start(): bool
    {
        if ($this->started) {
            return true;
        }

        if (session_status() === PHP_SESSION_ACTIVE) {
            $this->started = true;
            return true;
        }

        $result = session_start();
        
        if ($result) {
            $this->started = true;
            $this->validateSession();
        }

        return $result;
    }

    /**
     * Validate session security
     */
    private function validateSession(): void
    {
        // Regenerate session ID periodically for security
        if (!$this->has('_last_regenerated')) {
            $this->regenerateId();
        } elseif (time() - $this->get('_last_regenerated', 0) > 300) { // 5 minutes
            $this->regenerateId();
        }

        // Validate user agent and IP for security
        $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $currentIp = $this->getClientIp();

        if ($this->has('_user_agent') && $this->get('_user_agent') !== $currentUserAgent) {
            $this->invalidate();
            return;
        }

        if ($this->has('_ip_address') && $this->get('_ip_address') !== $currentIp) {
            $this->invalidate();
            return;
        }

        // Store security information
        $this->set('_user_agent', $currentUserAgent);
        $this->set('_ip_address', $currentIp);
    }

    /**
     * Get client IP address
     */
    private function getClientIp(): string
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim(explode(',', $_SERVER[$key])[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * Set session value
     */
    public function set(string $key, $value): void
    {
        $this->start();
        $_SESSION[$key] = $value;
    }

    /**
     * Get session value
     */
    public function get(string $key, $default = null)
    {
        $this->start();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if session key exists
     */
    public function has(string $key): bool
    {
        $this->start();
        return isset($_SESSION[$key]);
    }

    /**
     * Remove session key
     */
    public function remove(string $key): void
    {
        $this->start();
        unset($_SESSION[$key]);
    }

    /**
     * Get all session data
     */
    public function all(): array
    {
        $this->start();
        return $_SESSION;
    }

    /**
     * Flash data to session (available for next request only)
     */
    public function flash(string $key, $value): void
    {
        $this->set('_flash.' . $key, $value);
    }

    /**
     * Get flash data
     */
    public function getFlash(string $key, $default = null)
    {
        $value = $this->get('_flash.' . $key, $default);
        $this->remove('_flash.' . $key);
        return $value;
    }

    /**
     * Regenerate session ID
     */
    public function regenerateId(bool $deleteOld = true): bool
    {
        $this->start();
        $result = session_regenerate_id($deleteOld);
        
        if ($result) {
            $this->set('_last_regenerated', time());
        }
        
        return $result;
    }

    /**
     * Generate CSRF token
     */
    public function regenerateToken(): string
    {
        $token = bin2hex(random_bytes(32));
        $this->set('_token', $token);
        return $token;
    }

    /**
     * Get CSRF token
     */
    public function getToken(): string
    {
        if (!$this->has('_token')) {
            return $this->regenerateToken();
        }
        
        return $this->get('_token');
    }

    /**
     * Validate CSRF token
     */
    public function validateToken(string $token): bool
    {
        return hash_equals($this->getToken(), $token);
    }

    /**
     * Invalidate session
     */
    public function invalidate(): bool
    {
        $this->start();
        
        // Clear session data
        $_SESSION = [];
        
        // Delete session cookie
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params['path'],
                $params['domain'],
                $params['secure'],
                $params['httponly']
            );
        }
        
        // Destroy session
        $result = session_destroy();
        $this->started = false;
        
        return $result;
    }

    /**
     * Get session ID
     */
    public function getId(): string
    {
        $this->start();
        return session_id();
    }

    /**
     * Set session ID
     */
    public function setId(string $id): void
    {
        session_id($id);
    }

    /**
     * Check if session is started
     */
    public function isStarted(): bool
    {
        return $this->started;
    }

    /**
     * Save session data
     */
    public function save(): void
    {
        if ($this->started) {
            session_write_close();
            $this->started = false;
        }
    }
}
