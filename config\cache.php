<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Cache Store
    |--------------------------------------------------------------------------
    */
    'default' => env('CACHE_DRIVER', 'file'),

    /*
    |--------------------------------------------------------------------------
    | Cache Stores
    |--------------------------------------------------------------------------
    */
    'stores' => [
        'file' => [
            'driver' => 'file',
            'path' => storage_path('cache/data'),
            'ttl' => env('CACHE_TTL', 3600),
        ],

        'database' => [
            'driver' => 'database',
            'table' => 'cache_entries',
            'connection' => null,
            'ttl' => env('CACHE_TTL', 3600),
        ],

        'array' => [
            'driver' => 'array',
            'serialize' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefix
    |--------------------------------------------------------------------------
    */
    'prefix' => env('CACHE_PREFIX', 'jobspace_cache'),

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    */
    'enabled' => env('CACHE_ENABLED', true),
    'ttl' => env('CACHE_TTL', 3600), // 1 hour
    'cleanup_probability' => env('CACHE_CLEANUP_PROBABILITY', 2), // 2% chance
    'cleanup_divisor' => env('CACHE_CLEANUP_DIVISOR', 100),

    /*
    |--------------------------------------------------------------------------
    | View Cache
    |--------------------------------------------------------------------------
    */
    'views' => [
        'enabled' => env('VIEW_CACHE', true),
        'path' => storage_path('cache/views'),
        'ttl' => env('VIEW_CACHE_TTL', 86400), // 24 hours
    ],

    /*
    |--------------------------------------------------------------------------
    | Query Cache
    |--------------------------------------------------------------------------
    */
    'queries' => [
        'enabled' => env('QUERY_CACHE', true),
        'ttl' => env('QUERY_CACHE_TTL', 1800), // 30 minutes
        'tags' => ['queries'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Cache
    |--------------------------------------------------------------------------
    */
    'sessions' => [
        'enabled' => env('SESSION_CACHE', true),
        'ttl' => env('SESSION_CACHE_TTL', 7200), // 2 hours
    ],

    /*
    |--------------------------------------------------------------------------
    | API Response Cache
    |--------------------------------------------------------------------------
    */
    'api' => [
        'enabled' => env('API_CACHE', true),
        'ttl' => env('API_CACHE_TTL', 300), // 5 minutes
        'tags' => ['api'],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Data Cache
    |--------------------------------------------------------------------------
    */
    'users' => [
        'enabled' => env('USER_CACHE', true),
        'ttl' => env('USER_CACHE_TTL', 3600), // 1 hour
        'tags' => ['users'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Content Cache
    |--------------------------------------------------------------------------
    */
    'content' => [
        'enabled' => env('CONTENT_CACHE', true),
        'ttl' => env('CONTENT_CACHE_TTL', 1800), // 30 minutes
        'tags' => ['content'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'compression' => env('CACHE_COMPRESSION', true),
        'serialization' => env('CACHE_SERIALIZATION', 'php'), // php, json, igbinary
        'max_file_size' => env('CACHE_MAX_FILE_SIZE', 10485760), // 10MB
        'directory_levels' => env('CACHE_DIRECTORY_LEVELS', 2),
    ],

    /*
    |--------------------------------------------------------------------------
    | Tagging
    |--------------------------------------------------------------------------
    */
    'tagging' => [
        'enabled' => env('CACHE_TAGGING', true),
        'separator' => env('CACHE_TAG_SEPARATOR', ':'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'enabled' => env('CACHE_MONITORING', true),
        'log_hits' => env('CACHE_LOG_HITS', false),
        'log_misses' => env('CACHE_LOG_MISSES', false),
        'statistics' => env('CACHE_STATISTICS', true),
    ],
];
