<?php

declare(strict_types=1);

namespace App\Core;

/**
 * Multi-layer File-based Cache System
 * High-performance caching with auto-invalidation and tagging
 * 
 * @package App\Core
 */
class Cache
{
    private string $cachePath;
    private int $defaultTtl;
    private bool $enabled;
    private array $tags = [];

    public function __construct()
    {
        $this->cachePath = CACHE_PATH;
        $this->defaultTtl = (int) ($_ENV['CACHE_TTL'] ?? 3600); // 1 hour
        $this->enabled = (bool) ($_ENV['CACHE_ENABLED'] ?? true);

        // Ensure cache directory exists
        if (!is_dir($this->cachePath)) {
            mkdir($this->cachePath, 0755, true);
        }

        // Create subdirectories for organization
        $this->createCacheDirectories();
    }

    /**
     * Create cache subdirectories
     */
    private function createCacheDirectories(): void
    {
        $directories = [
            'data',
            'views',
            'queries',
            'sessions',
            'tags'
        ];

        foreach ($directories as $dir) {
            $path = $this->cachePath . '/' . $dir;
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
        }
    }

    /**
     * Store data in cache
     */
    public function set(string $key, $value, int $ttl = null, array $tags = []): bool
    {
        if (!$this->enabled) {
            return false;
        }

        $ttl = $ttl ?? $this->defaultTtl;
        $expiry = time() + $ttl;

        $cacheData = [
            'value' => $value,
            'expiry' => $expiry,
            'created' => time(),
            'tags' => $tags
        ];

        $cacheFile = $this->getCacheFile($key);
        $cacheDir = dirname($cacheFile);

        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }

        // Store cache data
        $success = file_put_contents($cacheFile, serialize($cacheData), LOCK_EX) !== false;

        // Store tag associations
        if ($success && !empty($tags)) {
            $this->storeTags($key, $tags);
        }

        return $success;
    }

    /**
     * Retrieve data from cache
     */
    public function get(string $key, $default = null)
    {
        if (!$this->enabled) {
            return $default;
        }

        $cacheFile = $this->getCacheFile($key);

        if (!file_exists($cacheFile)) {
            return $default;
        }

        $cacheData = unserialize(file_get_contents($cacheFile));

        if (!$cacheData || !is_array($cacheData)) {
            $this->delete($key);
            return $default;
        }

        // Check if cache has expired
        if (isset($cacheData['expiry']) && time() > $cacheData['expiry']) {
            $this->delete($key);
            return $default;
        }

        return $cacheData['value'] ?? $default;
    }

    /**
     * Check if cache key exists and is valid
     */
    public function has(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }

        $cacheFile = $this->getCacheFile($key);

        if (!file_exists($cacheFile)) {
            return false;
        }

        $cacheData = unserialize(file_get_contents($cacheFile));

        if (!$cacheData || !is_array($cacheData)) {
            $this->delete($key);
            return false;
        }

        // Check if cache has expired
        if (isset($cacheData['expiry']) && time() > $cacheData['expiry']) {
            $this->delete($key);
            return false;
        }

        return true;
    }

    /**
     * Delete cache entry
     */
    public function delete(string $key): bool
    {
        $cacheFile = $this->getCacheFile($key);

        if (file_exists($cacheFile)) {
            // Remove tag associations
            $this->removeFromTags($key);
            return unlink($cacheFile);
        }

        return true;
    }

    /**
     * Clear all cache or cache by pattern
     */
    public function clear(string $pattern = '*'): bool
    {
        if ($pattern === '*') {
            return $this->clearAll();
        }

        $files = glob($this->cachePath . '/data/' . $pattern . '.cache');
        $success = true;

        foreach ($files as $file) {
            if (!unlink($file)) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Clear all cache
     */
    private function clearAll(): bool
    {
        return $this->deleteDirectory($this->cachePath . '/data') &&
               $this->deleteDirectory($this->cachePath . '/tags');
    }

    /**
     * Get or set cache with callback
     */
    public function remember(string $key, callable $callback, int $ttl = null, array $tags = [])
    {
        $value = $this->get($key);

        if ($value !== null) {
            return $value;
        }

        $value = $callback();
        $this->set($key, $value, $ttl, $tags);

        return $value;
    }

    /**
     * Increment cache value
     */
    public function increment(string $key, int $value = 1): int
    {
        $current = (int) $this->get($key, 0);
        $new = $current + $value;
        $this->set($key, $new);
        return $new;
    }

    /**
     * Decrement cache value
     */
    public function decrement(string $key, int $value = 1): int
    {
        $current = (int) $this->get($key, 0);
        $new = max(0, $current - $value);
        $this->set($key, $new);
        return $new;
    }

    /**
     * Cache data with tags
     */
    public function tags(array $tags): self
    {
        $instance = clone $this;
        $instance->tags = $tags;
        return $instance;
    }

    /**
     * Flush cache by tags
     */
    public function flush(array $tags = null): bool
    {
        if ($tags === null) {
            $tags = $this->tags;
        }

        if (empty($tags)) {
            return false;
        }

        $success = true;

        foreach ($tags as $tag) {
            $tagFile = $this->cachePath . '/tags/' . md5($tag) . '.tag';
            
            if (file_exists($tagFile)) {
                $keys = unserialize(file_get_contents($tagFile));
                
                if (is_array($keys)) {
                    foreach ($keys as $key) {
                        if (!$this->delete($key)) {
                            $success = false;
                        }
                    }
                }
                
                unlink($tagFile);
            }
        }

        return $success;
    }

    /**
     * Get cache file path
     */
    private function getCacheFile(string $key): string
    {
        $hash = md5($key);
        $dir = substr($hash, 0, 2);
        return $this->cachePath . '/data/' . $dir . '/' . $hash . '.cache';
    }

    /**
     * Store tag associations
     */
    private function storeTags(string $key, array $tags): void
    {
        foreach ($tags as $tag) {
            $tagFile = $this->cachePath . '/tags/' . md5($tag) . '.tag';
            $keys = [];

            if (file_exists($tagFile)) {
                $keys = unserialize(file_get_contents($tagFile)) ?: [];
            }

            if (!in_array($key, $keys)) {
                $keys[] = $key;
                file_put_contents($tagFile, serialize($keys), LOCK_EX);
            }
        }
    }

    /**
     * Remove key from tag associations
     */
    private function removeFromTags(string $key): void
    {
        $tagFiles = glob($this->cachePath . '/tags/*.tag');

        foreach ($tagFiles as $tagFile) {
            $keys = unserialize(file_get_contents($tagFile));
            
            if (is_array($keys) && in_array($key, $keys)) {
                $keys = array_filter($keys, function($k) use ($key) {
                    return $k !== $key;
                });
                
                if (empty($keys)) {
                    unlink($tagFile);
                } else {
                    file_put_contents($tagFile, serialize($keys), LOCK_EX);
                }
            }
        }
    }

    /**
     * Delete directory recursively
     */
    private function deleteDirectory(string $dir): bool
    {
        if (!is_dir($dir)) {
            return true;
        }

        $files = array_diff(scandir($dir), ['.', '..']);

        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            
            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }

        return rmdir($dir);
    }

    /**
     * Get cache statistics
     */
    public function getStats(): array
    {
        $dataDir = $this->cachePath . '/data';
        $tagDir = $this->cachePath . '/tags';

        $stats = [
            'enabled' => $this->enabled,
            'total_files' => 0,
            'total_size' => 0,
            'expired_files' => 0,
            'valid_files' => 0,
            'tags_count' => 0
        ];

        // Count data files
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dataDir, \RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'cache') {
                $stats['total_files']++;
                $stats['total_size'] += $file->getSize();

                // Check if expired
                $cacheData = unserialize(file_get_contents($file->getPathname()));
                if (isset($cacheData['expiry']) && time() > $cacheData['expiry']) {
                    $stats['expired_files']++;
                } else {
                    $stats['valid_files']++;
                }
            }
        }

        // Count tag files
        $tagFiles = glob($tagDir . '/*.tag');
        $stats['tags_count'] = count($tagFiles);

        return $stats;
    }

    /**
     * Clean expired cache entries
     */
    public function cleanup(): int
    {
        $dataDir = $this->cachePath . '/data';
        $cleaned = 0;

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dataDir, \RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'cache') {
                $cacheData = unserialize(file_get_contents($file->getPathname()));
                
                if (isset($cacheData['expiry']) && time() > $cacheData['expiry']) {
                    unlink($file->getPathname());
                    $cleaned++;
                }
            }
        }

        return $cleaned;
    }
}
