<?php

use App\Core\Database;

return new class {
    /**
     * Run the migration
     */
    public function up(Database $db): void
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS user_roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                role_name VARCHAR(50) NOT NULL,
                granted_by INT NULL,
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
                UNIQUE KEY unique_user_role (user_id, role_name),
                INDEX idx_user_id (user_id),
                INDEX idx_role_name (role_name),
                INDEX idx_expires_at (expires_at)
            ) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
        ";
        
        $db->execute($sql);
    }

    /**
     * Reverse the migration
     */
    public function down(Database $db): void
    {
        $db->execute("DROP TABLE IF EXISTS user_roles");
    }
};
